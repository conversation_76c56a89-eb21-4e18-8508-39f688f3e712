# 加密货币资讯趋势前端项目

这是一个基于 Taro 4.x 的多端应用，支持微信小程序、H5 和 PC 端，专注于加密货币资讯和趋势展示。

## 🚀 技术栈

- **框架**: Taro 4.x + React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Zustand
- **网络请求**: Axios
- **UI 框架**: Material UI + Tailwind CSS
- **包管理器**: pnpm
- **样式预处理**: Sass/SCSS

## 📱 支持平台

- ✅ 微信小程序
- ✅ H5 (移动端 + PC 端)
- ✅ 支付宝小程序
- ✅ 百度小程序
- ✅ 字节跳动小程序
- ✅ QQ 小程序
- ✅ 京东小程序

## 🎯 核心功能

### 1. 响应式设计
- 移动端和小程序使用 rem 弹性布局
- PC 端使用媒体查询适配
- 完美支持三端（小程序、H5、PC）

### 2. 主题系统
- 支持明暗主题切换
- 自动检测系统主题
- 主题状态持久化

### 3. 性能优化
- 🦴 骨架屏加载
- 📦 分包处理
- 🖼️ 图片懒加载
- 🧩 组件懒加载
- 🗜️ 图片压缩
- 📦 代码压缩

### 4. 网络请求
- Axios 请求拦截器
- 统一错误处理
- 加载状态管理
- 平台适配

### 5. 状态管理
- Zustand 全局状态
- 主题状态管理
- 用户状态管理
- 数据状态管理
- 应用状态管理

## 🛠️ 开发指南

### 环境要求

- Node.js >= 16
- pnpm >= 7

### 安装依赖

```bash
pnpm install
```

### 开发命令

```bash
# 微信小程序开发
pnpm dev:weapp

# H5 开发
pnpm dev:h5

# 支付宝小程序开发
pnpm dev:alipay

# 百度小程序开发
pnpm dev:swan

# 字节跳动小程序开发
pnpm dev:tt

# QQ 小程序开发
pnpm dev:qq

# 京东小程序开发
pnpm dev:jd
```

### 构建命令

```bash
# 微信小程序构建
pnpm build:weapp

# H5 构建
pnpm build:h5

# 生产环境构建（带优化）
pnpm build:h5:prod
pnpm build:weapp:prod

# 构建分析
pnpm build:h5:analyze
```

### 其他命令

```bash
# 代码检查
pnpm lint

# 代码修复
pnpm lint:fix

# 类型检查
pnpm type-check

# 图片优化
pnpm optimize:images

# 清理构建文件
pnpm clean
```

## 📁 项目结构

```
src/
├── components/          # 公共组件
│   ├── Layout/         # 布局组件
│   ├── LazyLoad/       # 懒加载组件
│   ├── Platform/       # 平台适配组件
│   ├── Skeleton/       # 骨架屏组件
│   └── ThemeProvider.tsx
├── hooks/              # 自定义 Hooks
│   ├── useResponsive.ts
│   └── useLazyLoad.ts
├── pages/              # 页面
│   ├── index/          # 首页
│   └── news/           # 新闻页
├── services/           # API 服务
│   ├── api.ts
│   └── request.ts
├── stores/             # 状态管理
│   ├── themeStore.ts
│   ├── userStore.ts
│   ├── appStore.ts
│   ├── dataStore.ts
│   └── index.ts
├── styles/             # 全局样式
│   ├── globals.scss
│   └── platform.scss
├── types/              # 类型定义
│   └── index.ts
├── utils/              # 工具函数
│   ├── platform.ts
│   └── responsive.ts
├── app.config.ts       # 应用配置
├── app.scss           # 应用样式
└── app.tsx            # 应用入口
```

## 🎨 样式系统

### CSS 变量
项目使用 CSS 变量实现主题切换：

```css
:root {
  --color-primary: #1976d2;
  --color-secondary: #dc004e;
  --color-background: #ffffff;
  --color-surface: #f5f5f5;
  --color-text-primary: #212121;
  --color-text-secondary: #757575;
}
```

### 响应式断点
```javascript
const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  xxl: 1536,
}
```

### 平台特定样式
```scss
// 小程序端
.platform-weapp {
  // 小程序特有样式
}

// H5 端
.platform-h5 {
  // H5 特有样式
}
```

## 🔧 配置说明

### Tailwind CSS 配置
- 支持小程序端编译
- 自定义断点和颜色
- 禁用预设样式避免冲突

### Taro 配置
- 支持 TypeScript
- Vite 构建优化
- 分包配置
- 平台适配

### 构建优化
- 代码分割
- 图片压缩
- CSS 压缩
- Tree Shaking

## 📱 平台适配

### 小程序端
- 使用 rpx 单位
- 适配小程序组件
- 处理平台差异

### H5 端
- 响应式设计
- 媒体查询
- 现代浏览器特性

### PC 端
- 大屏适配
- 鼠标交互
- 键盘导航

## 🚀 部署

### H5 部署
构建后的文件在 `dist` 目录，可直接部署到静态服务器。

### 小程序部署
1. 使用微信开发者工具打开 `dist` 目录
2. 预览和上传代码
3. 提交审核

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系

如有问题，请提交 Issue 或联系开发团队。
