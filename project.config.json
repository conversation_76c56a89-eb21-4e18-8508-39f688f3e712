{"miniprogramRoot": "dist/", "projectname": "crypto-taro", "description": "", "appid": "wxba851da9ee252bda", "setting": {"urlCheck": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "preloadBackgroundData": false, "minified": false, "newFeature": true, "autoAudits": false, "coverView": true, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "useCompilerModule": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "libVersion": "3.9.3", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}}