import type { UserConfigExport } from '@tarojs/cli';

export default {
  defineConstants: {
    NODE_ENV: '"production"',
  },
  mini: {},
  h5: {
    legacy: true,
    // publicPath: '/',
    // staticDirectory: 'static',
    // output: {
    //   filename: 'js/[name].[hash:8].js',
    //   chunkFilename: 'js/[name].[contenthash:8].chunk.js',
    // },
    // miniCssExtractPluginOption: {
    //   ignoreOrder: true,
    //   filename: 'css/[name].[contenthash:8].css',
    //   chunkFilename: 'css/[name].[contenthash:8].chunk.css',
    // },
    // webpackChain(chain) {
    //   // 生产环境优化
    //   chain.optimization.minimize(true).splitChunks({
    //     chunks: 'all',
    //     maxInitialRequests: Infinity,
    //     minSize: 0,
    //     cacheGroups: {
    //       default: {
    //         minChunks: 2,
    //         priority: -20,
    //         reuseExistingChunk: true,
    //       },
    //       vendor: {
    //         test: /[\\/]node_modules[\\/]/,
    //         name: 'vendor',
    //         priority: -10,
    //         reuseExistingChunk: true,
    //       },
    //       common: {
    //         name: 'common',
    //         minChunks: 2,
    //         priority: -30,
    //         reuseExistingChunk: true,
    //       },
    //       react: {
    //         test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
    //         name: 'react',
    //         priority: 20,
    //         reuseExistingChunk: true,
    //       },
    //       mui: {
    //         test: /[\\/]node_modules[\\/]@mui[\\/]/,
    //         name: 'mui',
    //         priority: 15,
    //         reuseExistingChunk: true,
    //       },
    //     },
    //   });

    //   // 图片优化
    //   chain.module
    //     .rule('images')
    //     .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
    //     .use('url-loader')
    //     .loader('url-loader')
    //     .options({
    //       limit: 8192,
    //       name: 'static/images/[name].[contenthash:8].[ext]',
    //     });

    //   // 字体优化
    //   chain.module
    //     .rule('fonts')
    //     .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/)
    //     .use('url-loader')
    //     .loader('url-loader')
    //     .options({
    //       limit: 8192,
    //       name: 'static/fonts/[name].[contenthash:8].[ext]',
    //     });
    // },
  },
} satisfies UserConfigExport<'vite'>;
