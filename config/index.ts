import { defineConfig, type UserConfigExport } from '@tarojs/cli';
import vitePluginImp from 'vite-plugin-imp';
import { UnifiedViteWeappTailwindcssPlugin } from 'weapp-tailwindcss/vite';
import devConfig from './dev';
import prodConfig from './prod';

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig<'vite'>(async (merge, { command, mode }) => {
  const baseConfig: UserConfigExport<'vite'> = {
    projectName: 'crypto-taro',
    date: '2025-7-31',
    designWidth: 375,
    deviceRatio: {
      640: 2.34 / 2,
      750: 1,
      375: 2,
      828: 1.81 / 2,
    },
    sourceRoot: 'src',
    outputRoot: 'dist',
    plugins: ['@tarojs/plugin-html'],
    defineConstants: {},
    copy: {
      patterns: [],
      options: {},
    },
    framework: 'react',
    compiler: {
      type: 'vite',
      vitePlugins: [
        vitePluginImp({
          libList: [
            {
              libName: '@nutui/nutui-react-taro',
              style: name => {
                return `@nutui/nutui-react-taro/dist/esm/${name}/style/css`;
              },
              replaceOldImport: false,
              camel2DashComponentName: false,
            },
          ],
        }),
        {
          name: 'postcss-config-loader-plugin',
          config(config) {
            // 加载 tailwindcss
            if (typeof config.css?.postcss === 'object') {
              const tailwindcss = require('@tailwindcss/postcss');
              // @ts-ignore
              config.css?.postcss.plugins?.unshift(tailwindcss());
            }
          },
        },
        UnifiedViteWeappTailwindcssPlugin({
          rem2rpx: true,
          // appType: 'taro'
        }),
      ],
    },
    mini: {
      optimizeMainPackage: {
        enable: true,
        exclude: ['@tarojs/taro', 'react', 'react-dom'],
      },
      postcss: {
        pxtransform: {
          enable: true,
          config: {
            selectorBlackList: ['nut-', 'van-', 'weui-'],
            unitPrecision: 5,
            propList: ['*'],
            minPixelValue: 0,
            mediaQuery: false,
          },
        },
        cssModules: {
          enable: false,
          config: {
            namingPattern: 'module',
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
        autoprefixer: {
          enable: true,
          config: {
            overrideBrowserslist: [
              'last 3 versions',
              'Android >= 4.1',
              'ios >= 8',
            ],
          },
        },
      },
      webpackChain(chain) {
        // 小程序端代码压缩优化
        chain.optimization.minimize(true);

        // 分包优化
        chain.optimization.splitChunks({
          chunks: 'all',
          maxInitialRequests: Infinity,
          minSize: 0,
          cacheGroups: {
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendor',
              priority: -10,
              reuseExistingChunk: true,
            },
            common: {
              name: 'common',
              minChunks: 2,
              priority: -30,
              reuseExistingChunk: true,
            },
          },
        });
        // 小程序端优化配置
        chain.optimization.splitChunks({
          chunks: 'all',
          maxInitialRequests: Infinity,
          minSize: 0,
          cacheGroups: {
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendor',
              priority: -10,
              reuseExistingChunk: true,
            },
          },
        });
      },
    },
    h5: {
      publicPath: '/',
      staticDirectory: 'static',
      esnextModules: ['@tarojs/components'],
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: 'css/[name].[hash].css',
        chunkFilename: 'css/[name].[chunkhash].css',
      },
      devServer: {
        host: 'localhost',
        port: 8080,
        proxy: {
          '/api': {
            target: 'http://localhost:9527/',
            changeOrigin: true,
            secure: false,
            rewrite: path => path.replace(/^\/api/, '/api'),
          },
        },
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {
            overrideBrowserslist: [
              'last 3 versions',
              'Android >= 4.1',
              'ios >= 8',
            ],
          },
        },
        pxtransform: {
          enable: true,
          config: {
            selectorBlackList: ['nut-', 'van-', 'weui-'],
            unitPrecision: 5,
            propList: ['*'],
            minPixelValue: 0,
            mediaQuery: false,
          },
        },
        cssModules: {
          enable: false,
          config: {
            namingPattern: 'module',
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
      },
      webpackChain(chain) {
        // H5端优化配置
        chain.optimization.splitChunks({
          chunks: 'all',
          maxInitialRequests: Infinity,
          minSize: 0,
          cacheGroups: {
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendor',
              priority: -10,
              reuseExistingChunk: true,
            },
            common: {
              name: 'common',
              minChunks: 2,
              priority: -30,
              reuseExistingChunk: true,
            },
          },
        });

        // 添加响应式图片处理
        chain.module
          .rule('images')
          .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
          .use('url-loader')
          .loader('url-loader')
          .options({
            limit: 8192,
            name: 'static/images/[name].[hash:8].[ext]',
          });
      },
    },
    rn: {
      appName: 'taroDemo',
      postcss: {
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        },
      },
    },
  };

  process.env.BROWSERSLIST_ENV = process.env.NODE_ENV;

  if (process.env.NODE_ENV === 'development') {
    // 本地开发构建配置（不混淆压缩）
    return merge({}, baseConfig, devConfig);
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge({}, baseConfig, prodConfig);
});
