export interface NewsItemType {
  id: string;
  title: string;
  summary: string;
  content: string;
  url: string;
  source: string;
  published_at: string;
  fetched_at: string;
  is_mock: boolean;
}

export interface NewsListParams {
  query: string;
  page_size: number;
  page: number;
  sort_by: string;
  language: string;
}

export interface NewsListResponse {
  success: boolean;
  data: {
    query: string;
    page_size: number;
    page: number;
    sort_by: string;
    totalResults: number;
    articles: NewsDetailResponse[];
  };
}
export interface NewsDetailResponse {
  id: string;
  title: string;
  description: string;
  content: string;
  url: string;
  source: string;
  published_at: string;
  fetched_at: string;
  is_mock: boolean;
}

export type NewsCategory = 'bitcoin' | 'ethereum' | 'defi' | 'nft';
