export interface AnalysisResponseType {
  success: boolean;
  message: string;
  request_time: string;
  processing_time: number;
  results: AnalysisResultsType[];
  total_coins: number;
  successful_analyses: number;
}

export interface AnalysisResultsType {
  success: boolean;
  coin_id: string;
  coin_name: string;
  coin_symbol: string;
  analysis_time: string;
  current_price?: number;
  price_change_24h: number;
  coin: CoinType;
  price: CoinPriceType;
  news_count: number;
  metadata: MetadataType;
  news_list: NewsListItem[];
  url_list: string[];
  model_info: ModelInfoType;
  news_analysis: NewsAnalysisItemType[];
  analysis: AnalysisType;
  overall_sentiment?: string;
  overall_sentiment_score?: number;
  short_term_trend?: TermTrendType;
  mid_term_trend?: TermTrendType;
  strategy?: string[];
  risk_warning?: string;
  summary_of_news?: string;
  summary_of_news_en?: string;
  summary_brief: string;
  links_to_reference_news_articles?: string[];
  timestamp: string;
  error_message: string;
}

export interface CoinType {
  id: string;
  name: string;
  symbol: string;
}

export interface CoinPriceType {
  current_price: number;
  price_change_24h: number;
  currency: string;
}

export interface AnalysisType {
  impact: string;
  impact_score: number;
  trend_prediction: string;
  risk_warning: string;
  detailed_analysis: string;
  confidence_level: string;
}

export interface MetadataType {
  news_count: number;
  model_used: string;
  analysis_time: string;
  data_freshness: string;
  heuristic_overall_sentiment: string;
  heuristic_overall_sentiment_score: number;
  tone: string;
  audience: string;
}

export interface NewsListItem {
  id: string;
  title: string;
  description: string;
  source: string;
  publishedAt: string;
  fetched_at: string;
  is_mock: boolean;
}

export interface ModelInfoType {
  provider: string;
  model_name: string;
  base_url: string;
  supports_streaming: boolean;
  max_tokens: number;
  context_length: number;
}

export interface TermTrendType {
  direction: string;
  reason: string;
}

export interface NewsAnalysisItemType {
  title: string;
  sentiment: string;
  sentiment_score: number;
  reason: string;
}
