// 基础类型定义
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// 分页参数
export interface PaginationParams {
  page?: number;
  limit?: number;
  total?: number;
}

// 分页响应
export interface PaginationResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface BaseRequest {
  coins: string[];
  model:
    | 'deepseek-chat'
    | 'deepseek-reasoner'
    | 'qwen-plus'
    | 'qwen-turbo'
    | 'qwen-max';
  provider: 'deepseek' | 'qwen';
  news_days: number;
  news_limit?: number;
}

// 加密货币类型
export interface Cryptocurrency extends BaseEntity {
  name: string;
  symbol: string;
  price: number;
  marketCap: number;
  volume24h?: number;
  change24h?: number;
  change7d?: number;
  change30d?: number;
  imageUrl?: string;
  rank: number;
  supply: {
    circulating: number;
    total: number;
    max: number;
  };
  description?: string;
  website?: string;
  whitepaper?: string;
  tags: string[];
}

// 新闻类型
export interface NewsItemType extends BaseEntity {
  title: string;
  description: string;
  content?: string;
  url: string;
  imageUrl: string;
  source: string;
  id: string;
  author?: string;
  publishedAt: string;
  relatedCoins: string[];
  tags: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  views: number;
  likes: number;
  shares: number;
  comments: number;
}

// 趋势话题类型
export interface TrendingTopic extends BaseEntity {
  name: string;
  count: number;
  sentiment: 'positive' | 'negative' | 'neutral';
  relatedCoins: string[];
  description?: string;
  change24h: number;
  volume: number;
}

// 用户类型
export interface User extends BaseEntity {
  username: string;
  email: string;
  avatar?: string;
  bio?: string;
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    notifications: {
      email: boolean;
      push: boolean;
      priceAlerts: boolean;
      newsAlerts: boolean;
    };
  };
  watchlist: string[]; // 关注的加密货币ID列表
  portfolio: {
    coinId: string;
    amount: number;
    averagePrice: number;
  }[];
}

// 主题类型
export interface Theme {
  mode: 'light' | 'dark';
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: {
    primary: string;
    secondary: string;
  };
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 网络类型定义
export type NetworkType =
  | 'wifi'
  | '2g'
  | '3g'
  | '4g'
  | '5g'
  | 'none'
  | 'unknown';

export type Platform =
  | 'weapp'
  | 'h5'
  | 'rn'
  | 'swan'
  | 'alipay'
  | 'tt'
  | 'qq'
  | 'jd';

// 响应式断点
export interface Breakpoints {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// 加载状态
export interface LoadingState {
  isLoading: boolean;
  error: AppError | null;
}

// 搜索参数
export interface SearchParams {
  keyword: string;
  filters?: {
    category?: string;
    dateRange?: {
      start: string;
      end: string;
    };
    priceRange?: {
      min: number;
      max: number;
    };
    sentiment?: 'positive' | 'negative' | 'neutral';
  };
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
}

// 图表数据类型
export interface ChartDataPoint {
  timestamp: number;
  value: number;
  label?: string;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor?: string;
    backgroundColor?: string;
    fill?: boolean;
  }[];
}

// 通知类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: {
    label: string;
    action: () => void;
  }[];
}

// 导出所有类型
export * from '../services/api';
