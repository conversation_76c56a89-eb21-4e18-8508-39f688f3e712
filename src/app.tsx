import { useDidHide, useDidShow } from '@tarojs/taro';
import { useEffect } from 'react';
import PlatformProvider from './components/Platform/PlatformProvider';
import ThemeProvider from './components/ThemeProvider';
import { initStores } from './stores';
// 全局样式
import './app.scss';
import './components/Skeleton/skeleton.scss';
import './styles/globals.scss';
import './styles/platform.scss';

function App(props) {
  // 初始化所有store - 异步优化
  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const initializeStores = async () => {
      try {
        cleanup = await initStores();
      } catch (error) {
        console.error('Failed to initialize stores:', error);
      }
    };

    initializeStores();

    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, []);

  // 对应 onShow
  useDidShow(() => {
    console.log('App onShow');
  });

  // 对应 onHide
  useDidHide(() => {
    console.log('App onHide');
  });

  return (
    <PlatformProvider>
      <ThemeProvider>{props.children}</ThemeProvider>
    </PlatformProvider>
  );
}

export default App;
