import { useCallback, useEffect, useRef, useState } from 'react';
import { useInView } from 'react-intersection-observer';

// 懒加载状态接口
interface LazyLoadState {
  isLoaded: boolean;
  isLoading: boolean;
  error: Error | null;
}

// 懒加载选项接口
interface LazyLoadOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  delay?: number;
  enabled?: boolean;
}

// 基础懒加载hook
export const useLazyLoad = (options: LazyLoadOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    triggerOnce = true,
    delay = 0,
    enabled = true,
  } = options;

  const [state, setState] = useState<LazyLoadState>({
    isLoaded: false,
    isLoading: false,
    error: null,
  });

  const { ref, inView } = useInView({
    threshold,
    rootMargin,
    triggerOnce,
  });

  const load = useCallback(
    async (loadFn: () => Promise<void>) => {
      if (!enabled || state.isLoaded || state.isLoading) return;

      setState(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        await loadFn();
        setState(prev => ({ ...prev, isLoaded: true, isLoading: false }));
      } catch (error) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: error as Error,
        }));
      }
    },
    [enabled, state.isLoaded, state.isLoading, delay]
  );

  return {
    ref,
    inView,
    ...state,
    load,
    reset: () => setState({ isLoaded: false, isLoading: false, error: null }),
  };
};

// 图片懒加载hook
export const useLazyImage = (src: string, options: LazyLoadOptions = {}) => {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const { ref, inView, load } = useLazyLoad(options);

  useEffect(() => {
    if (inView && src) {
      load(async () => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = () => {
            setImageSrc(src);
            setIsLoaded(true);
            resolve();
          };
          img.onerror = () => {
            const error = new Error(`Failed to load image: ${src}`);
            setError(error);
            reject(error);
          };
          img.src = src;
        });
      });
    }
  }, [inView, src, load]);

  return {
    ref,
    imageSrc,
    isLoaded,
    error,
    inView,
  };
};

// 数据懒加载hook
export const useLazyData = <T>(
  fetchFn: () => Promise<T>,
  options: LazyLoadOptions = {}
) => {
  const [data, setData] = useState<T | null>(null);
  const { ref, inView, isLoaded, isLoading, error, load } =
    useLazyLoad(options);

  useEffect(() => {
    if (inView) {
      load(async () => {
        const result = await fetchFn();
        setData(result);
      });
    }
  }, [inView, load, fetchFn]);

  return {
    ref,
    data,
    isLoaded,
    isLoading,
    error,
    inView,
  };
};

// 组件懒加载hook
export const useLazyComponent = (options: LazyLoadOptions = {}) => {
  const { ref, inView, isLoaded, load } = useLazyLoad(options);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (inView) {
      load(async () => {
        setShouldRender(true);
      });
    }
  }, [inView, load]);

  return {
    ref,
    shouldRender: shouldRender || isLoaded,
    inView,
  };
};

// 无限滚动hook
export const useInfiniteScroll = <T>(
  fetchFn: (page: number) => Promise<T[]>,
  options: LazyLoadOptions & { initialPage?: number; pageSize?: number } = {}
) => {
  const { initialPage = 1, pageSize = 10, ...lazyOptions } = options;
  const [data, setData] = useState<T[]>([]);
  const [page, setPage] = useState(initialPage);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const { ref, inView } = useInView({
    threshold: 0.1,
    rootMargin: '100px',
  });

  const loadMore = useCallback(async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    setError(null);

    try {
      const newData = await fetchFn(page);

      if (newData.length < pageSize) {
        setHasMore(false);
      }

      setData(prev => [...prev, ...newData]);
      setPage(prev => prev + 1);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchFn, page, pageSize, isLoading, hasMore]);

  useEffect(() => {
    if (inView && hasMore && !isLoading) {
      loadMore();
    }
  }, [inView, hasMore, isLoading, loadMore]);

  const reset = useCallback(() => {
    setData([]);
    setPage(initialPage);
    setHasMore(true);
    setIsLoading(false);
    setError(null);
  }, [initialPage]);

  return {
    ref,
    data,
    isLoading,
    error,
    hasMore,
    loadMore,
    reset,
  };
};

// 预加载hook
export const usePreload = () => {
  const preloadedRefs = useRef<Set<string>>(new Set());

  const preloadImage = useCallback((src: string): Promise<void> => {
    if (preloadedRefs.current.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        preloadedRefs.current.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  }, []);

  const preloadImages = useCallback(
    (srcs: string[]): Promise<void[]> => {
      return Promise.all(srcs.map(preloadImage));
    },
    [preloadImage]
  );

  const isPreloaded = useCallback((src: string): boolean => {
    return preloadedRefs.current.has(src);
  }, []);

  return {
    preloadImage,
    preloadImages,
    isPreloaded,
  };
};

// 视口可见性hook
export const useVisibility = (options: LazyLoadOptions = {}) => {
  const { threshold = 0.1, rootMargin = '0px', triggerOnce = false } = options;

  const { ref, inView } = useInView({
    threshold,
    rootMargin,
    triggerOnce,
  });

  return {
    ref,
    isVisible: inView,
  };
};

// 延迟执行hook
export const useDelayedExecution = (
  callback: () => void,
  delay: number,
  dependencies: React.DependencyList = []
) => {
  useEffect(() => {
    const timer = setTimeout(callback, delay);
    return () => clearTimeout(timer);
  }, [callback, delay, ...dependencies]);
};
