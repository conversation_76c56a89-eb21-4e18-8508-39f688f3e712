import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { useAppStore } from '../stores/appStore';
import type { DeviceType } from '../types';
import {
  breakpoints,
  isDesktop,
  isMobile,
  isTablet,
} from '../utils/responsive';

// 响应式状态接口
interface ResponsiveState {
  screenWidth: number;
  screenHeight: number;
  deviceType: DeviceType;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
}

// 使用响应式状态的hook
export const useResponsive = (): ResponsiveState => {
  const { screenWidth, screenHeight, deviceType } = useAppStore();

  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>(
    'portrait'
  );

  useEffect(() => {
    // 计算屏幕方向
    const newOrientation =
      screenWidth > screenHeight ? 'landscape' : 'portrait';
    setOrientation(newOrientation);
  }, [screenWidth, screenHeight]);

  return {
    screenWidth,
    screenHeight,
    deviceType,
    isMobile: isMobile(screenWidth),
    isTablet: isTablet(screenWidth),
    isDesktop: isDesktop(screenWidth),
    orientation,
  };
};

// 断点匹配hook
export const useBreakpoint = () => {
  const { screenWidth } = useAppStore();

  const matches = {
    xs: screenWidth >= breakpoints.xs,
    sm: screenWidth >= breakpoints.sm,
    md: screenWidth >= breakpoints.md,
    lg: screenWidth >= breakpoints.lg,
    xl: screenWidth >= breakpoints.xl,
    xxl: screenWidth >= breakpoints.xxl,
  };

  const current: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' = (() => {
    if (screenWidth >= breakpoints.xxl) return 'xxl';
    if (screenWidth >= breakpoints.xl) return 'xl';
    if (screenWidth >= breakpoints.lg) return 'lg';
    if (screenWidth >= breakpoints.md) return 'md';
    if (screenWidth >= breakpoints.sm) return 'sm';
    return 'xs';
  })();

  return {
    matches,
    current,
    screenWidth,
  };
};

// 媒体查询hook
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    // 在H5环境下使用原生媒体查询
    if (
      process.env.TARO_ENV === 'h5' &&
      typeof window !== 'undefined' &&
      window.matchMedia
    ) {
      const mediaQuery = window.matchMedia(query);
      setMatches(mediaQuery.matches);

      const handleChange = (e: MediaQueryListEvent) => {
        setMatches(e.matches);
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      // 在小程序环境下模拟媒体查询
      const { screenWidth } = useAppStore.getState();

      // 简单的媒体查询解析
      const minWidthMatch = query.match(/min-width:\s*(\d+)px/);
      const maxWidthMatch = query.match(/max-width:\s*(\d+)px/);

      let result = true;

      if (minWidthMatch) {
        const minWidth = parseInt(minWidthMatch[1], 10);
        result = result && screenWidth >= minWidth;
      }

      if (maxWidthMatch) {
        const maxWidth = parseInt(maxWidthMatch[1], 10);
        result = result && screenWidth <= maxWidth;
      }

      setMatches(result);
    }
  }, [query]);

  return matches;
};

// 响应式值hook
export const useResponsiveValue = <T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  xxl?: T;
}): T | undefined => {
  const { current } = useBreakpoint();

  // 根据当前断点返回对应的值
  const breakpointOrder = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'] as const;
  const currentIndex = breakpointOrder.indexOf(current);

  // 从当前断点开始向下查找第一个有值的断点
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const breakpoint = breakpointOrder[i];
    if (values[breakpoint] !== undefined) {
      return values[breakpoint];
    }
  }

  return undefined;
};

// 屏幕尺寸变化监听hook
export const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState({
    width: 375,
    height: 667,
  });

  useEffect(() => {
    const updateScreenSize = () => {
      try {
        const systemInfo = Taro.getSystemInfoSync();
        setScreenSize({
          width: systemInfo.screenWidth,
          height: systemInfo.screenHeight,
        });
      } catch (error) {
        console.warn('Failed to get screen size:', error);
      }
    };

    // 初始化
    updateScreenSize();

    // 监听屏幕尺寸变化（主要在H5环境下有效）
    if (process.env.TARO_ENV === 'h5' && typeof window !== 'undefined') {
      const handleResize = () => {
        updateScreenSize();
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  return screenSize;
};

// 安全区域hook
export const useSafeArea = () => {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  });

  useEffect(() => {
    try {
      const systemInfo = Taro.getSystemInfoSync();
      setSafeArea({
        top: systemInfo.safeArea?.top || 0,
        bottom:
          systemInfo.screenHeight -
          (systemInfo.safeArea?.bottom || systemInfo.screenHeight),
        left: systemInfo.safeArea?.left || 0,
        right:
          systemInfo.screenWidth -
          (systemInfo.safeArea?.right || systemInfo.screenWidth),
      });
    } catch (error) {
      console.warn('Failed to get safe area:', error);
    }
  }, []);

  return safeArea;
};

// 设备方向hook
export const useOrientation = () => {
  const { screenWidth, screenHeight } = useResponsive();

  return {
    orientation: screenWidth > screenHeight ? 'landscape' : 'portrait',
    isPortrait: screenHeight > screenWidth,
    isLandscape: screenWidth > screenHeight,
  };
};

// 响应式字体大小hook
export const useResponsiveFontSize = () => {
  const { deviceType } = useResponsive();

  const baseFontSize = (() => {
    switch (deviceType) {
      case 'mobile':
        return 14;
      case 'tablet':
        return 16;
      case 'desktop':
        return 18;
      default:
        return 16;
    }
  })();

  return {
    baseFontSize,
    scale: (size: number) => size * (baseFontSize / 16),
    rem: (px: number) => px / baseFontSize,
  };
};
