import { useMemo, useRef, useEffect } from 'react'
import { debounce, throttle, type DebounceOptions, type ThrottleOptions } from '../utils/timing'

// 基于函数的 Hook：返回可取消/flush 的回调
export function useDebouncedCallback<T extends (...args: any[]) => any> (
  fn: T,
  options?: DebounceOptions
) {
  const fnRef = useRef(fn)
  useEffect(() => { fnRef.current = fn }, [fn])

  const debounced = useMemo(() => {
    const wrapped = debounce((...args: Parameters<T>) => fnRef.current(...args), options)
    return wrapped
  }, [options])

  useEffect(() => () => { debounced.cancel() }, [debounced])

  return debounced
}

export function useThrottledCallback<T extends (...args: any[]) => any> (
  fn: T,
  options?: ThrottleOptions
) {
  const fnRef = useRef(fn)
  useEffect(() => { fnRef.current = fn }, [fn])

  const throttled = useMemo(() => {
    return throttle((...args: Parameters<T>) => fnRef.current(...args), options)
  }, [options])

  return throttled
}

