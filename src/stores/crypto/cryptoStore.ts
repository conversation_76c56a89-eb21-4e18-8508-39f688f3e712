import { create } from 'zustand';
import {
  createJSONStorage,
  persist,
  subscribeWithSelector,
} from 'zustand/middleware';
import type { CryptoItem } from '../../components/CryptoElevator/types';
import api from '../../services/api';
import type {
  AnalysisResponse,
  BaseRequest,
  CoinConfig,
  Cryptocurrency,
} from '../../types';
import { createZustandStorage } from '../../utils/storage';
import { throttle } from '../../utils/timing';
import { useLoadingStore } from '../loadingStore';

// 加密货币状态接口
interface CryptoState {
  // 加密货币数据
  cryptocurrencies: Cryptocurrency[];
  cryptoError: string | null;
  cryptoLastUpdated: number | null;

  // 市场分析相关
  analysisResult: AnalysisResponse | null;
  analysisLoading: boolean;
  analysisError: string | null;

  // 电梯组件相关
  cryptoSearchResults: CryptoItem[];
  cryptoSearchLoading: boolean;
  cryptoSearchError: string | null;
  selectedCoinsForAnalysis: CryptoItem | null;
  selectedCoinsForProfile: CryptoItem[];

  // 缓存设置
  cacheExpiry: number; // 缓存过期时间（毫秒）
}

// 加密货币动作接口
interface CryptoActions {
  // 加密货币相关
  fetchCryptocurrencies: (params?: {
    page?: number;
    limit?: number;
    forceRefresh?: boolean;
  }) => Promise<void>;
  getCryptocurrencyById: (id: string) => Cryptocurrency | undefined;
  updateCryptocurrency: (id: string, updates: Partial<Cryptocurrency>) => void;
  fetchCryptoAnalysisTrend: (data: BaseRequest) => Promise<any>;

  // 市场分析相关
  performAnalysis: (request: any) => Promise<void>;
  clearAnalysisResult: () => void;

  // 电梯组件相关
  searchCryptos: (key?: string) => Promise<void>;
  setSelectedCoinForAnalysis: (coin: CryptoItem | null) => void;
  setSelectedCoinsForProfile: (coins: CryptoItem[]) => void;
  addCoinToProfile: (coin: CryptoItem) => boolean;
  removeCoinFromProfile: (coinId: string) => void;
  clearCryptoSearch: () => void;

  // 缓存管理
  isCacheValid: (lastUpdated: number | null) => boolean;
  clearCryptoCache: () => void;

  // 错误处理
  clearCryptoErrors: () => void;
  clearCryptoError: (type: 'crypto' | 'analysis') => void;
}

// 加载状态 ID 常量
const CRYPTO_LOADING_IDS = {
  CRYPTO_LIST: 'crypto-list',
  CRYPTO_ANALYSIS: 'crypto-analysis',
} as const;

// 创建加密货币store
export const useCryptoStore = create<CryptoState & CryptoActions>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        // 初始状态
        cryptocurrencies: [],
        cryptoError: null,
        cryptoLastUpdated: null,

        // 市场分析初始状态
        analysisResult: null,
        analysisLoading: false,
        analysisError: null,

        // 电梯组件初始状态
        cryptoSearchResults: [],
        cryptoSearchLoading: false,
        cryptoSearchError: null,
        selectedCoinsForAnalysis: null,
        selectedCoinsForProfile: [],

        cacheExpiry: 5 * 60 * 1000, // 5分钟缓存

        // 缓存管理
        isCacheValid: (lastUpdated: number | null) => {
          if (!lastUpdated) return false;
          const { cacheExpiry } = get();
          return Date.now() - lastUpdated < cacheExpiry;
        },

        clearCryptoCache: () => {
          set({
            cryptocurrencies: [],
            cryptoLastUpdated: null,
          });
        },

        // 加密货币分析趋势
        fetchCryptoAnalysisTrend: async (data: BaseRequest) => {
          const { coins } = data;
          if (!coins) return;

          return api.crypto.getAnalysisTrend(data);
        },

        // 加密货币相关方法
        fetchCryptocurrencies: throttle(async (params = {}) => {
          const { forceRefresh = false } = params;
          const { cryptoLastUpdated, isCacheValid } = get();

          // 检查缓存
          if (!forceRefresh && isCacheValid(cryptoLastUpdated)) {
            return;
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            CRYPTO_LOADING_IDS.CRYPTO_LIST,
            '获取加密货币列表...'
          );

          try {
            set({ cryptoError: null });

            // 模拟进度更新
            loadingStore.updateProgress(CRYPTO_LOADING_IDS.CRYPTO_LIST, 30);

            // TODO: 替换为新的数据源，暂时使用模拟数据
            console.warn(
              'fetchCryptocurrencies: 使用模拟数据，需要替换为新的数据源'
            );

            // 暂时返回空数据，避免错误
            const cryptocurrencies: any[] = [];

            loadingStore.updateProgress(CRYPTO_LOADING_IDS.CRYPTO_LIST, 80);

            set({
              cryptocurrencies: (
                cryptocurrencies as unknown as CoinConfig[]
              ).map((crypto: any) => ({
                ...crypto,
                rank: 0,
                supply: {
                  circulating: 0,
                  total: 0,
                  max: 0,
                },
                tags: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              })),
              cryptoLastUpdated: Date.now(),
              cryptoError: null,
            });

            loadingStore.finishLoading(
              CRYPTO_LOADING_IDS.CRYPTO_LIST,
              'success'
            );
          } catch (error: any) {
            set({
              cryptoError: error.message || '获取加密货币数据失败',
            });
            loadingStore.finishLoading(CRYPTO_LOADING_IDS.CRYPTO_LIST, 'error');
            throw error;
          }
        }),

        getCryptocurrencyById: (id: string) => {
          const { cryptocurrencies } = get();
          return [...cryptocurrencies].find(crypto => crypto.id === id);
        },

        updateCryptocurrency: (
          id: string,
          updates: Partial<Cryptocurrency>
        ) => {
          set(state => ({
            cryptocurrencies: state.cryptocurrencies.map(crypto =>
              crypto.id === id ? { ...crypto, ...updates } : crypto
            ),
          }));
        },

        // 市场分析相关方法
        performAnalysis: async (request: any) => {
          const { startLoading, clearLoading } = useLoadingStore.getState();

          try {
            set({
              analysisLoading: true,
              analysisError: null,
              analysisResult: null,
            });
            startLoading('analysis', 'AI 正在分析中，请稍候...');

            const response = await api.crypto.analyze(request);
            const analysisData = response.data;

            if (analysisData.success === true) {
              set({
                analysisResult: analysisData,
                analysisLoading: false,
              });
            } else {
              throw new Error(analysisData.message || '分析失败');
            }
          } catch (error) {
            console.error('分析失败:', error);
            set({
              analysisError:
                error instanceof Error ? error.message : '分析失败',
              analysisLoading: false,
            });
          } finally {
            clearLoading('analysis');
          }
        },

        clearAnalysisResult: () => {
          set({
            analysisResult: null,
            analysisError: null,
          });
        },

        // 电梯组件相关方法
        searchCryptos: async (key?: string) => {
          try {
            set({ cryptoSearchLoading: true, cryptoSearchError: null });

            const response = await api.crypto.search({
              key,
            });

            if (response.success && response.data) {
              set({
                cryptoSearchResults: response.data.results,
                cryptoSearchLoading: false,
              });
            } else {
              throw new Error('搜索失败');
            }
          } catch (error) {
            console.error('搜索币种失败:', error);
            set({
              cryptoSearchError:
                error instanceof Error ? error.message : '搜索失败',
              cryptoSearchLoading: false,
            });
          }
        },

        setSelectedCoinForAnalysis: (coin: CryptoItem | null) => {
          set({ selectedCoinsForAnalysis: coin });
        },

        setSelectedCoinsForProfile: (coins: CryptoItem[]) => {
          set({ selectedCoinsForProfile: coins });
        },

        addCoinToProfile: (coin: CryptoItem) => {
          const { selectedCoinsForProfile } = get();

          // 检查是否已存在
          if (selectedCoinsForProfile.some(c => c.id === coin.id)) {
            return false;
          }

          // 检查是否超过最大数量
          if (selectedCoinsForProfile.length >= 4) {
            return false;
          }

          set({
            selectedCoinsForProfile: [...selectedCoinsForProfile, coin],
          });
          return true;
        },

        removeCoinFromProfile: (coinId: string) => {
          const { selectedCoinsForProfile } = get();
          set({
            selectedCoinsForProfile: selectedCoinsForProfile.filter(
              c => c.id !== coinId
            ),
          });
        },

        clearCryptoSearch: () => {
          set({
            cryptoSearchResults: [],
            cryptoSearchError: null,
          });
        },

        // 错误处理
        clearCryptoErrors: () => {
          set({
            cryptoError: null,
            analysisError: null,
            cryptoSearchError: null,
          });
        },

        clearCryptoError: (type: 'crypto' | 'analysis') => {
          switch (type) {
            case 'crypto':
              set({ cryptoError: null });
              break;
            case 'analysis':
              set({ analysisError: null });
              break;
          }
        },
      }),
      {
        name: 'crypto-storage',
        storage: createJSONStorage(() => createZustandStorage()),
        // 选择性持久化，只保存数据，不保存错误状态
        partialize: state => ({
          cryptocurrencies: state.cryptocurrencies,
          cryptoLastUpdated: state.cryptoLastUpdated,
          selectedCoinsForAnalysis: state.selectedCoinsForAnalysis,
          selectedCoinsForProfile: state.selectedCoinsForProfile,
          cacheExpiry: state.cacheExpiry,
        }),
      }
    )
  )
);

// 选择器函数，用于优化组件订阅
export const cryptoSelectors = {
  // 加密货币选择器
  getCryptocurrencies: () => (state: CryptoState & CryptoActions) =>
    state.cryptocurrencies,
  getCryptoError: () => (state: CryptoState & CryptoActions) =>
    state.cryptoError,
  getCryptoLastUpdated: () => (state: CryptoState & CryptoActions) =>
    state.cryptoLastUpdated,

  // 市场分析选择器
  getAnalysisResult: () => (state: CryptoState & CryptoActions) =>
    state.analysisResult,
  getAnalysisLoading: () => (state: CryptoState & CryptoActions) =>
    state.analysisLoading,
  getAnalysisError: () => (state: CryptoState & CryptoActions) =>
    state.analysisError,

  // 电梯组件选择器
  getCryptoSearchResults: () => (state: CryptoState & CryptoActions) =>
    state.cryptoSearchResults,
  getCryptoSearchLoading: () => (state: CryptoState & CryptoActions) =>
    state.cryptoSearchLoading,
  getCryptoSearchError: () => (state: CryptoState & CryptoActions) =>
    state.cryptoSearchError,
  getSelectedCoinForAnalysis: () => (state: CryptoState & CryptoActions) =>
    state.selectedCoinsForAnalysis,
  getSelectedCoinsForProfile: () => (state: CryptoState & CryptoActions) =>
    state.selectedCoinsForProfile,
};

// 导出加载状态 ID
export { CRYPTO_LOADING_IDS };

// 初始化加密货币数据
export const initCryptoStore = () => {
  // 加密货币数据不需要在应用启动时立即加载，可以延迟加载
  // 这有助于减少首页白屏时间
};
