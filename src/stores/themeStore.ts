import Taro from '@tarojs/taro';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createZustandStorage } from '../utils/storage';

// 主题类型定义
export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  border: string;
  divider: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

// 预定义主题
const lightTheme: ThemeColors = {
  primary: '#1976d2',
  secondary: '#dc004e',
  background: '#ffffff',
  surface: '#f5f5f5',
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#bdbdbd',
  },
  border: '#e0e0e0',
  divider: '#e0e0e0',
  error: '#f44336',
  warning: '#ff9800',
  success: '#4caf50',
  info: '#2196f3',
};

const darkTheme: ThemeColors = {
  primary: '#90caf9',
  secondary: '#f48fb1',
  background: '#121212',
  surface: '#1e1e1e',
  text: {
    primary: '#ffffff',
    secondary: '#b3b3b3',
    disabled: '#666666',
  },
  border: '#333333',
  divider: '#333333',
  error: '#f44336',
  warning: '#ff9800',
  success: '#4caf50',
  info: '#2196f3',
};

// 状态接口
interface ThemeState {
  mode: ThemeMode;
  currentTheme: ThemeColors;
  systemTheme: 'light' | 'dark';
}

// 动作接口
interface ThemeActions {
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  setSystemTheme: (theme: 'light' | 'dark') => void;
  getCurrentTheme: () => ThemeColors;
}

// 获取系统主题
const getSystemTheme = (): 'light' | 'dark' => {
  try {
    const systemInfo = Taro.getSystemInfoSync();
    return systemInfo.theme === 'dark' ? 'dark' : 'light';
  } catch (error) {
    return 'light';
  }
};

// 计算当前主题
const calculateCurrentTheme = (
  mode: ThemeMode,
  systemTheme: 'light' | 'dark'
): ThemeColors => {
  if (mode === 'auto') {
    return systemTheme === 'dark' ? darkTheme : lightTheme;
  }
  return mode === 'dark' ? darkTheme : lightTheme;
};

// 创建主题store
export const useThemeStore = create(
  persist<ThemeState & ThemeActions>(
    (set, get) => ({
      mode: 'auto',
      systemTheme: getSystemTheme(),
      currentTheme: calculateCurrentTheme('auto', getSystemTheme()),

      setThemeMode: (mode: ThemeMode) => {
        const { systemTheme } = get();
        const currentTheme = calculateCurrentTheme(mode, systemTheme);

        set({ mode, currentTheme });

        // 更新页面样式
        updatePageTheme(currentTheme);
      },

      toggleTheme: () => {
        const { mode } = get();
        const newMode =
          mode === 'light' ? 'dark' : mode === 'dark' ? 'auto' : 'light';
        get().setThemeMode(newMode);
      },

      setSystemTheme: (systemTheme: 'light' | 'dark') => {
        const { mode } = get();
        const currentTheme = calculateCurrentTheme(mode, systemTheme);

        set({ systemTheme, currentTheme });

        // 如果是自动模式，更新页面样式
        if (mode === 'auto') {
          updatePageTheme(currentTheme);
        }
      },

      getCurrentTheme: () => {
        return get().currentTheme;
      },
    }),
    {
      name: 'theme-storage',
      storage: createZustandStorage(),
    }
  )
);

// 更新页面主题样式
const updatePageTheme = (theme: ThemeColors) => {
  try {
    // 在H5环境下更新CSS变量
    if (process.env.TARO_ENV === 'h5') {
      const root = document.documentElement;
      root.style.setProperty('--color-primary', theme.primary);
      root.style.setProperty('--color-secondary', theme.secondary);
      root.style.setProperty('--color-background', theme.background);
      root.style.setProperty('--color-surface', theme.surface);
      root.style.setProperty('--color-text-primary', theme.text.primary);
      root.style.setProperty('--color-text-secondary', theme.text.secondary);
      root.style.setProperty('--color-border', theme.border);
      root.style.setProperty('--color-divider', theme.divider);
    }

    // 在小程序环境下可以通过其他方式处理
    // 例如动态修改页面配置或使用全局样式类
  } catch (error) {
    console.warn('Failed to update page theme:', error);
  }
};

// 监听系统主题变化（仅在支持的环境下）
export const initThemeListener = () => {
  try {
    if (process.env.TARO_ENV === 'h5' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      const handleChange = (e: MediaQueryListEvent) => {
        const systemTheme = e.matches ? 'dark' : 'light';
        useThemeStore.getState().setSystemTheme(systemTheme);
      };

      mediaQuery.addEventListener('change', handleChange);

      // 返回清理函数
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  } catch (error) {
    console.warn('Failed to init theme listener:', error);
  }

  return () => {};
};

// 导出主题相关的工具函数
export const themeUtils = {
  lightTheme,
  darkTheme,
  getSystemTheme,
  calculateCurrentTheme,
  updatePageTheme,
};
