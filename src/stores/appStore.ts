import Taro from '@tarojs/taro';
import { create } from 'zustand';
import type { DeviceType, NetworkType, Platform } from '../types';

// 应用状态接口
interface AppState {
  platform: Platform;
  deviceType: DeviceType;
  screenWidth: number;
  screenHeight: number;
  statusBarHeight: number;
  isLoading: boolean;
  loadingText: string;
  networkStatus: 'wifi' | '2g' | '3g' | '4g' | '5g' | 'none' | 'unknown';
  isOnline: boolean;
}

// 应用动作接口
interface AppActions {
  setLoading: (loading: boolean, text?: string) => void;
  updateDeviceInfo: () => void;
  updateNetworkStatus: () => void;
  showToast: (
    title: string,
    icon?: 'success' | 'error' | 'loading' | 'none'
  ) => void;
  showModal: (title: string, content: string) => Promise<boolean>;
}

// 获取设备类型
const getDeviceType = (screenWidth: number): DeviceType => {
  if (screenWidth < 768) return 'mobile';
  if (screenWidth < 1024) return 'tablet';
  return 'desktop';
};

// 创建应用store
export const useAppStore = create<AppState & AppActions>((set, get) => ({
  platform: (process.env.TARO_ENV as Platform) || 'h5',
  deviceType: 'mobile',
  screenWidth: 375,
  screenHeight: 667,
  statusBarHeight: 0,
  isLoading: false,
  loadingText: '加载中...',
  networkStatus: 'unknown',
  isOnline: true,

  setLoading: (loading: boolean, text = '加载中...') => {
    set({ isLoading: loading, loadingText: text });

    if (loading) {
      Taro.showLoading({
        title: text,
        mask: true,
      });
    } else {
      Taro.hideLoading();
    }
  },

  updateDeviceInfo: () => {
    try {
      const systemInfo = Taro.getSystemInfoSync();
      const deviceType = getDeviceType(systemInfo.screenWidth);

      set({
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        statusBarHeight: systemInfo.statusBarHeight || 0,
        deviceType,
      });
    } catch (error) {
      console.warn('Failed to get system info:', error);
    }
  },

  updateNetworkStatus: () => {
    try {
      Taro.getNetworkType({
        success: res => {
          set({
            networkStatus: res.networkType as NetworkType,
            isOnline: res.networkType !== 'none',
          });
        },
        fail: () => {
          set({
            networkStatus: 'unknown',
            isOnline: true,
          });
        },
      });
    } catch (error) {
      console.warn('Failed to get network status:', error);
    }
  },

  showToast: (title: string, icon = 'none' as const) => {
    Taro.showToast({
      title,
      icon,
      duration: 2000,
    });
  },

  showModal: (title: string, content: string): Promise<boolean> => {
    return new Promise(resolve => {
      Taro.showModal({
        title,
        content,
        success: res => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        },
      });
    });
  },
}));

// 初始化应用状态
export const initAppStore = () => {
  const { updateDeviceInfo, updateNetworkStatus } = useAppStore.getState();

  // 更新设备信息
  updateDeviceInfo();

  // 更新网络状态
  updateNetworkStatus();

  // 监听网络状态变化
  try {
    Taro.onNetworkStatusChange(res => {
      useAppStore.setState({
        networkStatus: res.networkType as NetworkType,
        isOnline: res.isConnected,
      });
    });
  } catch (error) {
    console.warn('Failed to listen network status change:', error);
  }
};
