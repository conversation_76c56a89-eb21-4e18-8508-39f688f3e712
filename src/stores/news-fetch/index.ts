import { create } from 'zustand';
import {
  createJSONStorage,
  persist,
  subscribeWithSelector,
} from 'zustand/middleware';
import api from '../../services/api';
import { ResponseData } from '../../services/request';
import type { NewsItemType } from '../../types';
import { NewsListResponse } from '../../types/newsType';
import { createZustandStorage } from '../../utils/storage';
import { throttle } from '../../utils/timing';
import { useLoadingStore } from '../loadingStore';

// 新闻分类类型 - 移除trending，简化为query-based分类
export type NewsCategory = 'bitcoin' | 'ethereum' | 'defi' | 'nft';

// 新闻数据状态接口
interface NewsState {
  // 新闻数据 - 简化结构，支持分页
  news: NewsItemType[];
  categorizedNews: Record<NewsCategory, NewsItemType[]>;
  newsError: string | null;
  newsLoading: boolean;
  currentCategory: NewsCategory;

  // 分页状态
  currentPage: Record<NewsCategory, number>; // 每个分类的当前页码
  hasMore: Record<NewsCategory, boolean>; // 每个分类是否还有更多数据
  isLoadingMore: boolean; // 是否正在加载更多
}

// 新闻数据动作接口
interface NewsActions {
  // 新闻相关
  fetchNewsByCategory: (
    category: NewsCategory,
    page?: number,
    append?: boolean
  ) => Promise<void>;
  loadMoreNews: (category: NewsCategory) => Promise<void>;
  refreshNews: (category: NewsCategory) => Promise<void>;
  setCurrentCategory: (category: NewsCategory) => void;
  fetchNewsDetail: (url: string) => Promise<NewsItemType>;

  // 错误处理
  clearError: () => void;

  // 重置状态
  resetCategory: (category: NewsCategory) => void;
}

// 加载状态 ID 常量 - 移除趋势和搜索
const NEWS_LOADING_IDS = {
  NEWS_LIST: 'news-list',
  NEWS_CATEGORY: 'news-category',
  NEWS_DETAIL: 'news-detail',
} as const;

// 新闻分类配置 - 简化配置
const NEWS_CATEGORIES: Record<NewsCategory, { name: string; query?: string }> =
  {
    bitcoin: { name: 'Bitcoin', query: 'bitcoin' },
    ethereum: { name: 'Ethereum', query: 'ethereum' },
    defi: { name: 'DeFi', query: 'defi' },
    nft: { name: 'NFT', query: 'nft' },
  };

// 创建新闻store
export const useNewsStore = create<NewsState & NewsActions>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        // 初始状态
        news: [],
        categorizedNews: {
          bitcoin: [],
          ethereum: [],
          defi: [],
          nft: [],
        },
        newsError: null,
        newsLoading: false,
        currentCategory: 'bitcoin',

        // 分页状态
        currentPage: {
          bitcoin: 1,
          ethereum: 1,
          defi: 1,
          nft: 1,
        },
        hasMore: {
          bitcoin: true,
          ethereum: true,
          defi: true,
          nft: true,
        },
        isLoadingMore: false,

        /**
         * 获取指定分类的新闻（支持分页）
         * @param category 新闻分类
         * @param page 页码，默认为1
         * @param append 是否追加到现有列表，默认为false（替换）
         */
        fetchNewsByCategory: throttle(
          async (category: NewsCategory, page = 1, append = false) => {
            const { currentPage, hasMore } = get();

            // 如果没有更多数据且不是强制刷新，直接返回
            if (page > 1 && !hasMore[category]) {
              return;
            }

            const loadingStore = useLoadingStore.getState();
            const loadingId =
              page === 1
                ? NEWS_LOADING_IDS.NEWS_CATEGORY
                : NEWS_LOADING_IDS.NEWS_LIST;

            loadingStore.startLoading(
              loadingId,
              page === 1
                ? `获取${NEWS_CATEGORIES[category].name}新闻...`
                : '加载更多新闻...'
            );

            try {
              set({
                newsError: null,
                newsLoading: page === 1,
                isLoadingMore: page > 1,
              });

              // 获取新闻数据
              const categoryQuery = NEWS_CATEGORIES[category].query;
              const response: ResponseData<NewsListResponse> =
                await api.news.getList({
                  page_size: 20,
                  query: categoryQuery as string,
                  page,
                  sort_by: 'publishedAt',
                  language: 'en',
                });

              const listPayload = response;
              const processedNews = (listPayload['articles'] || []).map(
                item => ({
                  id: item.id,
                  title: item.title,
                  content: item.content,
                  url: item.url,
                  imageUrl: '',
                  source: item.source,
                  author: undefined,
                  publishedAt: item.published_at,
                  relatedCoins: [],
                  createdAt: item.published_at,
                  updatedAt: item.fetched_at || item.published_at,
                })
              );

              // 判断是否还有更多数据
              const hasMoreData = processedNews.length === 20;

              set(state => ({
                newsLoading: false,
                isLoadingMore: false,
                categorizedNews: {
                  ...state.categorizedNews,
                  [category]: append
                    ? [...state.categorizedNews[category], ...processedNews]
                    : processedNews,
                },
                currentPage: {
                  ...state.currentPage,
                  [category]: page,
                },
                hasMore: {
                  ...state.hasMore,
                  [category]: hasMoreData,
                },
                newsError: null,
              }));

              loadingStore.finishLoading(loadingId, 'success');
            } catch (error: any) {
              set({
                newsError:
                  error.message ||
                  `获取${NEWS_CATEGORIES[category].name}新闻失败`,
                newsLoading: false,
                isLoadingMore: false,
              });
              loadingStore.finishLoading(loadingId, 'error');
              throw error;
            }
          }
        ),

        /**
         * 加载更多新闻
         * @param category 新闻分类
         */
        loadMoreNews: async (category: NewsCategory) => {
          const { currentPage, hasMore } = get();

          if (!hasMore[category]) {
            return;
          }

          const nextPage = currentPage[category] + 1;
          await get().fetchNewsByCategory(category, nextPage, true);
        },

        /**
         * 刷新新闻（重置分页并重新加载）
         * @param category 新闻分类
         */
        refreshNews: async (category: NewsCategory) => {
          // 重置分页状态
          set(state => ({
            currentPage: {
              ...state.currentPage,
              [category]: 1,
            },
            hasMore: {
              ...state.hasMore,
              [category]: true,
            },
          }));

          await get().fetchNewsByCategory(category, 1, false);
        },

        /**
         * 重置分类状态（清空数据和分页信息）
         * @param category 新闻分类
         */
        resetCategory: (category: NewsCategory) => {
          set(state => ({
            categorizedNews: {
              ...state.categorizedNews,
              [category]: [],
            },
            currentPage: {
              ...state.currentPage,
              [category]: 1,
            },
            hasMore: {
              ...state.hasMore,
              [category]: true,
            },
          }));
        },

        setCurrentCategory: (category: NewsCategory) => {
          set({ currentCategory: category });
        },
        // 新增新闻详情获取方法
        fetchNewsDetail: async (url: string) => {
          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            NEWS_LOADING_IDS.NEWS_DETAIL,
            '获取新闻详情...'
          );

          try {
            const response = await api.news.getDetail(url);
            const newsDetail = response.data;

            // 补充BaseEntity需要的字段，以符合types中NewsItemType接口
            const processedDetail: NewsItemType = {
              id: newsDetail.id,
              title: newsDetail.title,
              description: newsDetail.description,
              content: newsDetail.content,
              url: newsDetail.url,
              imageUrl: '',
              source: newsDetail.source,
              author: undefined,
              publishedAt: newsDetail.published_at,
              relatedCoins: [],
              createdAt: newsDetail.published_at,
              updatedAt: newsDetail.fetched_at || newsDetail.published_at,
              tags: [],
              sentiment: 'neutral',
              views: 0,
              likes: 0,
              shares: 0,
              comments: 0,
            };

            loadingStore.finishLoading(NEWS_LOADING_IDS.NEWS_DETAIL, 'success');
            return processedDetail;
          } catch (error: any) {
            loadingStore.finishLoading(NEWS_LOADING_IDS.NEWS_DETAIL, 'error');
            throw error;
          }
        },

        // 错误处理
        clearError: () => {
          set({
            newsError: null,
          });
        },
      }),
      {
        name: 'news-storage',
        storage: createJSONStorage(() => createZustandStorage()),
        // 选择性持久化，只保存数据，不保存错误状态
        partialize: state => ({
          news: state.news,
          categorizedNews: state.categorizedNews,
          currentCategory: state.currentCategory,
          currentPage: state.currentPage,
          hasMore: state.hasMore,
        }),
      }
    )
  )
);

// 选择器函数，用于优化组件订阅
export const newsSelectors = {
  // 新闻选择器
  getCategorizedNews:
    (category: NewsCategory) => (state: NewsState & NewsActions) =>
      state.categorizedNews[category],
  getCurrentCategoryNews: () => (state: NewsState & NewsActions) =>
    state.categorizedNews[state.currentCategory],
  getNewsError: () => (state: NewsState & NewsActions) => state.newsError,
  getNewsLoading: () => (state: NewsState & NewsActions) => state.newsLoading,
  getCurrentCategory: () => (state: NewsState & NewsActions) =>
    state.currentCategory,

  // 分页选择器
  getCurrentPage:
    (category: NewsCategory) => (state: NewsState & NewsActions) =>
      state.currentPage[category],
  getHasMore: (category: NewsCategory) => (state: NewsState & NewsActions) =>
    state.hasMore[category],
  getIsLoadingMore: () => (state: NewsState & NewsActions) =>
    state.isLoadingMore,
};

// 导出加载状态 ID 和分类配置
export { NEWS_CATEGORIES, NEWS_LOADING_IDS };

// 初始化新闻数据 - 简化为基础新闻获取
export const initNewsStore = () => {};
