import { create } from 'zustand';
import {
  createJSONStorage,
  persist,
  subscribeWithSelector,
} from 'zustand/middleware';
import api from '../../services/api';
import type { BaseConfig, BaseConfigResponse, SubscriptionPlan } from '../../types';
import { createZustandStorage } from '../../utils/storage';
import { useLoadingStore } from '../loadingStore';

// 配置状态接口
interface ConfigState {
  // 基本配置数据
  baseConfig: BaseConfig | null;
  configLoading: boolean;
  configError: string | null;
  configLastUpdated: number | null;

  // 缓存设置
  cacheExpiry: number; // 配置缓存过期时间（毫秒）
}

// 配置动作接口
interface ConfigActions {
  // 基本配置相关
  fetchBaseConfig: (forceRefresh?: boolean) => Promise<void>;
  
  // 获取配置项
  getTabs: () => string[];
  getDefaultModel: () => string;
  getDefaultPlan: () => string;
  getSubscriptionPlans: () => SubscriptionPlan[];
  
  // 缓存管理
  isCacheValid: (lastUpdated: number | null) => boolean;
  clearConfigCache: () => void;
  
  // 错误处理
  clearConfigError: () => void;
}

// 加载状态 ID 常量
const CONFIG_LOADING_IDS = {
  BASE_CONFIG: 'base-config',
} as const;

// 创建配置store
export const useConfigStore = create<ConfigState & ConfigActions>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        // 初始状态
        baseConfig: null,
        configLoading: false,
        configError: null,
        configLastUpdated: null,
        cacheExpiry: 30 * 60 * 1000, // 30分钟缓存（配置变化不频繁）

        // 缓存管理
        isCacheValid: (lastUpdated: number | null) => {
          if (!lastUpdated) return false;
          const { cacheExpiry } = get();
          return Date.now() - lastUpdated < cacheExpiry;
        },

        clearConfigCache: () => {
          set({
            baseConfig: null,
            configLastUpdated: null,
          });
        },

        // 获取基本配置
        fetchBaseConfig: async (forceRefresh = false) => {
          const { configLastUpdated, isCacheValid } = get();

          // 检查缓存
          if (!forceRefresh && isCacheValid(configLastUpdated)) {
            return;
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            CONFIG_LOADING_IDS.BASE_CONFIG,
            '获取基本配置...'
          );

          try {
            set({ configLoading: true, configError: null });

            const response = await api.config.getBaseConfig();
            const configData = response.data;

            if (configData.success && configData.data) {
              // 转换API响应格式为内部格式
              const baseConfig: BaseConfig = {
                tabs: configData.data.tabs,
                defaultModel: configData.data.default_model,
                defaultPlan: configData.data.default_plan,
                subscriptionPlans: configData.data.subscriptions_plan,
              };

              set({
                baseConfig,
                configLoading: false,
                configLastUpdated: Date.now(),
                configError: null,
              });

              loadingStore.finishLoading(CONFIG_LOADING_IDS.BASE_CONFIG, 'success');
            } else {
              throw new Error('获取基本配置失败');
            }
          } catch (error: any) {
            set({
              configError: error.message || '获取基本配置失败',
              configLoading: false,
            });
            loadingStore.finishLoading(CONFIG_LOADING_IDS.BASE_CONFIG, 'error');
            throw error;
          }
        },

        // 获取配置项的便捷方法
        getTabs: () => {
          const { baseConfig } = get();
          return baseConfig?.tabs || [];
        },

        getDefaultModel: () => {
          const { baseConfig } = get();
          return baseConfig?.defaultModel || 'deepseek';
        },

        getDefaultPlan: () => {
          const { baseConfig } = get();
          return baseConfig?.defaultPlan || 'FREE';
        },

        getSubscriptionPlans: () => {
          const { baseConfig } = get();
          return baseConfig?.subscriptionPlans || [];
        },

        // 错误处理
        clearConfigError: () => {
          set({ configError: null });
        },
      }),
      {
        name: 'config-storage',
        storage: createJSONStorage(() => createZustandStorage()),
        // 选择性持久化，只保存配置数据，不保存错误状态
        partialize: state => ({
          baseConfig: state.baseConfig,
          configLastUpdated: state.configLastUpdated,
          cacheExpiry: state.cacheExpiry,
        }),
      }
    )
  )
);

// 选择器函数，用于优化组件订阅
export const configSelectors = {
  // 基本配置选择器
  getBaseConfig: () => (state: ConfigState & ConfigActions) => state.baseConfig,
  getConfigLoading: () => (state: ConfigState & ConfigActions) => state.configLoading,
  getConfigError: () => (state: ConfigState & ConfigActions) => state.configError,
  getConfigLastUpdated: () => (state: ConfigState & ConfigActions) => state.configLastUpdated,
  
  // 配置项选择器
  getTabs: () => (state: ConfigState & ConfigActions) => state.getTabs(),
  getDefaultModel: () => (state: ConfigState & ConfigActions) => state.getDefaultModel(),
  getDefaultPlan: () => (state: ConfigState & ConfigActions) => state.getDefaultPlan(),
  getSubscriptionPlans: () => (state: ConfigState & ConfigActions) => state.getSubscriptionPlans(),
};

// 导出加载状态 ID
export { CONFIG_LOADING_IDS };

// 初始化配置store - 设置为最高优先级
export const initConfigStore = async () => {
  const { fetchBaseConfig } = useConfigStore.getState();
  
  try {
    // 优先获取基本配置，这是应用启动的关键数据
    await fetchBaseConfig();
  } catch (error) {
    console.error('Failed to init config store:', error);
    // 配置获取失败不应该阻止应用启动，但需要记录错误
  }
};
