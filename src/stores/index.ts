import { initAppStore } from './appStore';
import { initDataStore } from './dataStore';
import { initThemeListener } from './themeStore';
import { initUserStore } from './userStore';
import { initNewsStore } from './news-fetch';

// 导出所有store
export { initAppStore, useAppStore } from './appStore';
export { initDataStore, useDataStore, dataSelectors } from './dataStore';
export { initThemeListener, themeUtils, useThemeStore } from './themeStore';
export { initUserStore, useUserStore } from './userStore';
export { initNewsStore, useNewsStore, newsSelectors, NEWS_LOADING_IDS, NEWS_CATEGORIES } from './news-fetch';

// 初始化所有store
export const initStores = () => {
  // 初始化主题监听
  const cleanupThemeListener = initThemeListener();

  // 初始化应用状态
  initAppStore();

  // 初始化用户状态
  initUserStore();

  // 初始化数据状态
  initDataStore();

  // 初始化新闻状态（预取）
  initNewsStore();

  // 返回清理函数
  return () => {
    cleanupThemeListener();
  };
};
