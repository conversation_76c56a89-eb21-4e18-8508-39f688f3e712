// 导入各模块的初始化函数
import { initAppStore } from './app';
import { initNewsStore } from './news';
import { initThemeListener } from './theme';
import { initUserStore } from './user';

// 导出所有store - 按模块分类
export * from './app';
export * from './config';
export * from './crypto';
export * from './news';
export * from './theme';
export * from './user';

// 保持向后兼容的导出
export { dataSelectors, useDataStore } from './dataStore';
export { loadingSelectors, useLoadingStore } from './loadingStore';

// 初始化所有store - 优化启动顺序
export const initStores = async () => {
  // 1. 优先初始化基本配置（最高优先级）
  try {
    const { initConfigStore } = await import('./config');
    await initConfigStore();
  } catch (error) {
    console.error('Failed to init config store:', error);
  }

  // 2. 初始化主题监听
  const cleanupThemeListener = initThemeListener();

  // 3. 初始化应用状态
  initAppStore();

  // 4. 初始化用户状态
  initUserStore();

  // 5. 初始化加密货币状态（延迟加载）
  const { initCryptoStore } = await import('./crypto');
  initCryptoStore();

  // 6. 初始化新闻状态（延迟加载）
  initNewsStore();

  // 返回清理函数
  return () => {
    cleanupThemeListener();
  };
};
