import { create } from 'zustand';
import {
  createJSONStorage,
  persist,
  subscribeWithSelector,
} from 'zustand/middleware';
import api from '../services/api';
import type { User } from '../types';
import { createZustandStorage } from '../utils/storage';
import { useLoadingStore } from './loadingStore';

// 登录凭据接口
interface LoginCredentials {
  username: string;
  password: string;
}

// 注册数据接口
interface RegisterData {
  username: string;
  email: string;
  password: string;
}

// 用户状态接口
interface UserState {
  // 认证状态
  isAuthenticated: boolean;

  // 用户数据
  user: User | null;
  token: string | null;

  // 错误状态
  error: string | null;

  // 用户偏好和数据
  watchlist: string[]; // 关注的加密货币ID列表
  portfolio: Array<{
    cryptoId: string;
    amount: number;
    purchasePrice: number;
    purchaseDate: string;
  }>;

  // 缓存和时间戳
  lastUpdated: number | null;
  profileLastUpdated: number | null;
}

// 微信登录数据接口
interface WechatLoginData {
  code: string;
  userInfo?: any;
}

// 验证码登录数据接口
interface CodeLoginData {
  account: string;
  code: string;
  type: 'email' | 'phone';
}

// 用户动作接口
interface UserActions {
  // 认证相关
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  wechatLogin: (data: WechatLoginData) => Promise<void>;
  loginWithCode: (data: CodeLoginData) => Promise<void>;
  logout: () => void;

  // 用户资料相关
  updateProfile: (updates: Partial<User>) => Promise<void>;
  getUserProfile: (forceRefresh?: boolean) => Promise<void>;

  // 关注列表管理
  addToWatchlist: (cryptoId: string) => Promise<void>;
  removeFromWatchlist: (cryptoId: string) => Promise<void>;

  // 投资组合管理
  addToPortfolio: (item: {
    cryptoId: string;
    amount: number;
    purchasePrice: number;
  }) => Promise<void>;
  removeFromPortfolio: (cryptoId: string) => Promise<void>;
  updatePortfolioItem: (
    cryptoId: string,
    updates: Partial<{
      amount: number;
      purchasePrice: number;
    }>
  ) => Promise<void>;

  // 错误处理
  clearError: () => void;

  // 缓存管理
  isCacheValid: (lastUpdated: number | null, maxAge?: number) => boolean;
  clearCache: () => void;
}

// 加载状态 ID 常量
const USER_LOADING_IDS = {
  LOGIN: 'user-login',
  REGISTER: 'user-register',
  PROFILE_UPDATE: 'user-profile-update',
  PROFILE_FETCH: 'user-profile-fetch',
  WATCHLIST_ADD: 'user-watchlist-add',
  WATCHLIST_REMOVE: 'user-watchlist-remove',
  PORTFOLIO_ADD: 'user-portfolio-add',
  PORTFOLIO_REMOVE: 'user-portfolio-remove',
  PORTFOLIO_UPDATE: 'user-portfolio-update',
} as const;

// 创建用户store
export const useUserStore = create<UserState & UserActions>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        // 初始状态
        isAuthenticated: false,
        user: null,
        token: null,
        error: null,
        watchlist: [],
        portfolio: [],
        lastUpdated: null,
        profileLastUpdated: null,

        // 缓存管理
        isCacheValid: (lastUpdated: number | null, maxAge = 30 * 60 * 1000) => {
          if (!lastUpdated) return false;
          return Date.now() - lastUpdated < maxAge;
        },

        clearCache: () => {
          set({
            user: null,
            watchlist: [],
            portfolio: [],
            lastUpdated: null,
            profileLastUpdated: null,
          });
        },

        // 认证相关方法
        login: async (credentials: LoginCredentials) => {
          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(USER_LOADING_IDS.LOGIN, '正在登录...');

          try {
            set({ error: null });

            loadingStore.updateProgress(USER_LOADING_IDS.LOGIN, 30);

            const response = await api.user.login(credentials);
            const { user, token } = response.data;

            loadingStore.updateProgress(USER_LOADING_IDS.LOGIN, 80);

            set({
              isAuthenticated: true,
              user: {
                ...user,
                watchlist: [],
                portfolio: [],
                preferences: {
                  theme: user?.preferences?.theme || 'auto',
                  language: user?.preferences?.language || 'zh-CN',
                  notifications: {
                    email: user?.preferences?.notifications?.email ?? true,
                    push: user?.preferences?.notifications?.push ?? true,
                    priceAlerts:
                      user?.preferences?.notifications?.priceAlerts ?? true,
                    newsAlerts:
                      user?.preferences?.notifications?.newsAlerts ?? true,
                  },
                },
              },
              token,
              watchlist: user.watchlist || [],
              portfolio:
                user.portfolio?.map(item => ({
                  cryptoId: item.coinId,
                  amount: item.amount,
                  purchasePrice: item.averagePrice,
                  purchaseDate: new Date().toISOString(),
                })) || [],
              lastUpdated: Date.now(),
              profileLastUpdated: Date.now(),
              error: null,
            });

            loadingStore.finishLoading(USER_LOADING_IDS.LOGIN, 'success');
          } catch (error: any) {
            set({
              error: error.message || '登录失败',
              isAuthenticated: false,
              user: null,
              token: null,
            });
            loadingStore.finishLoading(USER_LOADING_IDS.LOGIN, 'error');
            throw error;
          }
        },

        register: async (data: RegisterData) => {
          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(USER_LOADING_IDS.REGISTER, '正在注册...');

          try {
            set({ error: null });

            loadingStore.updateProgress(USER_LOADING_IDS.REGISTER, 30);

            const response = await api.user.register(data);
            const { user, token } = response.data;

            loadingStore.updateProgress(USER_LOADING_IDS.REGISTER, 80);

            set({
              isAuthenticated: true,
              user: {
                ...user,
                watchlist: [],
                portfolio: [],
                preferences: {
                  theme: 'auto',
                  language: 'zh-CN',
                  notifications: {
                    email: true,
                    push: true,
                    priceAlerts: true,
                    newsAlerts: true,
                  },
                },
              },
              token,
              watchlist: [],
              portfolio: [],
              lastUpdated: Date.now(),
              profileLastUpdated: Date.now(),
              error: null,
            });

            loadingStore.finishLoading(USER_LOADING_IDS.REGISTER, 'success');
          } catch (error: any) {
            set({
              error: error.message || '注册失败',
            });
            loadingStore.finishLoading(USER_LOADING_IDS.REGISTER, 'error');
            throw error;
          }
        },

        wechatLogin: async (data: WechatLoginData) => {
          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(USER_LOADING_IDS.LOGIN, '微信登录中...');

          try {
            set({ error: null });

            loadingStore.updateProgress(USER_LOADING_IDS.LOGIN, 30);

            const response = await api.user.wechatLogin(data);
            const { user, token } = response.data;

            loadingStore.updateProgress(USER_LOADING_IDS.LOGIN, 80);

            set({
              isAuthenticated: true,
              user: {
                ...user,
                watchlist: user.watchlist || [],
                portfolio: user.portfolio || [],
                preferences: {
                  theme: user?.preferences?.theme || 'auto',
                  language: user?.preferences?.language || 'zh-CN',
                  notifications: {
                    email: user?.preferences?.notifications?.email ?? true,
                    push: user?.preferences?.notifications?.push ?? true,
                    priceAlerts:
                      user?.preferences?.notifications?.priceAlerts ?? true,
                    newsAlerts:
                      user?.preferences?.notifications?.newsAlerts ?? true,
                  },
                },
              },
              token,
              watchlist: user.watchlist || [],
              portfolio:
                user.portfolio?.map(item => ({
                  cryptoId: item.coinId,
                  amount: item.amount,
                  purchasePrice: item.averagePrice,
                  purchaseDate: new Date().toISOString(),
                })) || [],
              lastUpdated: Date.now(),
              profileLastUpdated: Date.now(),
              error: null,
            });

            loadingStore.finishLoading(USER_LOADING_IDS.LOGIN, 'success');
          } catch (error: any) {
            set({
              error: error.message || '微信登录失败',
              isAuthenticated: false,
              user: null,
              token: null,
            });
            loadingStore.finishLoading(USER_LOADING_IDS.LOGIN, 'error');
            throw error;
          }
        },

        loginWithCode: async (data: CodeLoginData) => {
          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(USER_LOADING_IDS.LOGIN, '验证码登录中...');

          try {
            set({ error: null });

            loadingStore.updateProgress(USER_LOADING_IDS.LOGIN, 30);

            const response = await api.user.loginWithCode(data);
            const { user, token } = response.data;

            loadingStore.updateProgress(USER_LOADING_IDS.LOGIN, 80);

            set({
              isAuthenticated: true,
              user: {
                ...user,
                watchlist: user.watchlist || [],
                portfolio: user.portfolio || [],
                preferences: {
                  theme: user?.preferences?.theme || 'auto',
                  language: user?.preferences?.language || 'zh-CN',
                  notifications: {
                    email: user?.preferences?.notifications?.email ?? true,
                    push: user?.preferences?.notifications?.push ?? true,
                    priceAlerts:
                      user?.preferences?.notifications?.priceAlerts ?? true,
                    newsAlerts:
                      user?.preferences?.notifications?.newsAlerts ?? true,
                  },
                },
              },
              token,
              watchlist: user.watchlist || [],
              portfolio:
                user.portfolio?.map(item => ({
                  cryptoId: item.coinId,
                  amount: item.amount,
                  purchasePrice: item.averagePrice,
                  purchaseDate: new Date().toISOString(),
                })) || [],
              lastUpdated: Date.now(),
              profileLastUpdated: Date.now(),
              error: null,
            });

            loadingStore.finishLoading(USER_LOADING_IDS.LOGIN, 'success');
          } catch (error: any) {
            set({
              error: error.message || '验证码登录失败',
              isAuthenticated: false,
              user: null,
              token: null,
            });
            loadingStore.finishLoading(USER_LOADING_IDS.LOGIN, 'error');
            throw error;
          }
        },

        logout: () => {
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            watchlist: [],
            portfolio: [],
            lastUpdated: null,
            profileLastUpdated: null,
            error: null,
          });
        },

        // 用户资料相关方法
        updateProfile: async (updates: Partial<User>) => {
          const { user } = get();
          if (!user) {
            throw new Error('用户未登录');
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            USER_LOADING_IDS.PROFILE_UPDATE,
            '更新资料中...'
          );

          try {
            set({ error: null });

            loadingStore.updateProgress(USER_LOADING_IDS.PROFILE_UPDATE, 40);

            const response = await api.user.updateProfile(updates);
            const updatedUser = response.data;

            loadingStore.updateProgress(USER_LOADING_IDS.PROFILE_UPDATE, 80);

            set({
              user: {
                ...updatedUser,
                watchlist: updatedUser.watchlist || get().watchlist,
                portfolio: updatedUser.portfolio || get().user?.portfolio || [],
              },
              profileLastUpdated: Date.now(),
              error: null,
            });

            loadingStore.finishLoading(
              USER_LOADING_IDS.PROFILE_UPDATE,
              'success'
            );
          } catch (error: any) {
            set({
              error: error.message || '更新资料失败',
            });
            loadingStore.finishLoading(
              USER_LOADING_IDS.PROFILE_UPDATE,
              'error'
            );
            throw error;
          }
        },

        getUserProfile: async (forceRefresh = false) => {
          const { user, profileLastUpdated, isCacheValid } = get();

          if (!user) {
            throw new Error('用户未登录');
          }

          // 检查缓存
          if (!forceRefresh && isCacheValid(profileLastUpdated)) {
            return;
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            USER_LOADING_IDS.PROFILE_FETCH,
            '获取用户资料...'
          );

          try {
            set({ error: null });

            loadingStore.updateProgress(USER_LOADING_IDS.PROFILE_FETCH, 50);

            const response = await api.user.getProfile();
            const userProfile = response.data;

            set({
              user: {
                ...userProfile,
                watchlist: userProfile.watchlist || [],
                portfolio: userProfile.portfolio || [],
              },
              watchlist: userProfile.watchlist || [],
              portfolio:
                userProfile.portfolio?.map(item => ({
                  cryptoId: item.coinId,
                  amount: item.amount,
                  purchasePrice: item.averagePrice,
                  purchaseDate: new Date().toISOString(),
                })) || [],
              profileLastUpdated: Date.now(),
              error: null,
            });

            loadingStore.finishLoading(
              USER_LOADING_IDS.PROFILE_FETCH,
              'success'
            );
          } catch (error: any) {
            set({
              error: error.message || '获取用户资料失败',
            });
            loadingStore.finishLoading(USER_LOADING_IDS.PROFILE_FETCH, 'error');
            throw error;
          }
        },

        // 关注列表管理
        addToWatchlist: async (cryptoId: string) => {
          const { user, watchlist } = get();
          if (!user) {
            throw new Error('用户未登录');
          }

          if (watchlist.includes(cryptoId)) {
            return; // 已经在关注列表中
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            USER_LOADING_IDS.WATCHLIST_ADD,
            '添加到关注列表...'
          );

          try {
            set({ error: null });

            const newWatchlist = [...watchlist, cryptoId];

            // 乐观更新
            set({ watchlist: newWatchlist });

            // 模拟 API 调用
            await new Promise(resolve => setTimeout(resolve, 500));

            loadingStore.finishLoading(
              USER_LOADING_IDS.WATCHLIST_ADD,
              'success'
            );
          } catch (error: any) {
            // 回滚乐观更新
            set({ watchlist });
            set({ error: error.message || '添加到关注列表失败' });
            loadingStore.finishLoading(USER_LOADING_IDS.WATCHLIST_ADD, 'error');
            throw error;
          }
        },

        removeFromWatchlist: async (cryptoId: string) => {
          const { user, watchlist } = get();
          if (!user) {
            throw new Error('用户未登录');
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            USER_LOADING_IDS.WATCHLIST_REMOVE,
            '从关注列表移除...'
          );

          try {
            set({ error: null });

            const newWatchlist = watchlist.filter(id => id !== cryptoId);

            // 乐观更新
            set({ watchlist: newWatchlist });

            // 模拟 API 调用
            await new Promise(resolve => setTimeout(resolve, 500));

            loadingStore.finishLoading(
              USER_LOADING_IDS.WATCHLIST_REMOVE,
              'success'
            );
          } catch (error: any) {
            // 回滚乐观更新
            set({ watchlist });
            set({ error: error.message || '从关注列表移除失败' });
            loadingStore.finishLoading(
              USER_LOADING_IDS.WATCHLIST_REMOVE,
              'error'
            );
            throw error;
          }
        },

        // 投资组合管理
        addToPortfolio: async (item: {
          cryptoId: string;
          amount: number;
          purchasePrice: number;
        }) => {
          const { user, portfolio } = get();
          if (!user) {
            throw new Error('用户未登录');
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            USER_LOADING_IDS.PORTFOLIO_ADD,
            '添加到投资组合...'
          );

          try {
            set({ error: null });

            const newPortfolioItem = {
              ...item,
              purchaseDate: new Date().toISOString(),
            };

            const newPortfolio = [...portfolio, newPortfolioItem];

            // 乐观更新
            set({ portfolio: newPortfolio });

            // 模拟 API 调用
            await new Promise(resolve => setTimeout(resolve, 500));

            loadingStore.finishLoading(
              USER_LOADING_IDS.PORTFOLIO_ADD,
              'success'
            );
          } catch (error: any) {
            // 回滚乐观更新
            set({ portfolio });
            set({ error: error.message || '添加到投资组合失败' });
            loadingStore.finishLoading(USER_LOADING_IDS.PORTFOLIO_ADD, 'error');
            throw error;
          }
        },

        removeFromPortfolio: async (cryptoId: string) => {
          const { user, portfolio } = get();
          if (!user) {
            throw new Error('用户未登录');
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            USER_LOADING_IDS.PORTFOLIO_REMOVE,
            '从投资组合移除...'
          );

          try {
            set({ error: null });

            const newPortfolio = portfolio.filter(
              item => item.cryptoId !== cryptoId
            );

            // 乐观更新
            set({ portfolio: newPortfolio });

            // 模拟 API 调用
            await new Promise(resolve => setTimeout(resolve, 500));

            loadingStore.finishLoading(
              USER_LOADING_IDS.PORTFOLIO_REMOVE,
              'success'
            );
          } catch (error: any) {
            // 回滚乐观更新
            set({ portfolio });
            set({ error: error.message || '从投资组合移除失败' });
            loadingStore.finishLoading(
              USER_LOADING_IDS.PORTFOLIO_REMOVE,
              'error'
            );
            throw error;
          }
        },

        updatePortfolioItem: async (
          cryptoId: string,
          updates: Partial<{
            amount: number;
            purchasePrice: number;
          }>
        ) => {
          const { user, portfolio } = get();
          if (!user) {
            throw new Error('用户未登录');
          }

          const loadingStore = useLoadingStore.getState();
          loadingStore.startLoading(
            USER_LOADING_IDS.PORTFOLIO_UPDATE,
            '更新投资组合...'
          );

          try {
            set({ error: null });

            const newPortfolio = portfolio.map(item =>
              item.cryptoId === cryptoId ? { ...item, ...updates } : item
            );

            // 乐观更新
            set({ portfolio: newPortfolio });

            // 模拟 API 调用
            await new Promise(resolve => setTimeout(resolve, 500));

            loadingStore.finishLoading(
              USER_LOADING_IDS.PORTFOLIO_UPDATE,
              'success'
            );
          } catch (error: any) {
            // 回滚乐观更新
            set({ portfolio });
            set({ error: error.message || '更新投资组合失败' });
            loadingStore.finishLoading(
              USER_LOADING_IDS.PORTFOLIO_UPDATE,
              'error'
            );
            throw error;
          }
        },

        // 错误处理
        clearError: () => {
          set({ error: null });
        },
      }),
      {
        name: 'user-storage',
        storage: createJSONStorage(() => createZustandStorage()),
        // 选择性持久化，只保存必要的用户数据
        partialize: state => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          token: state.token,
          watchlist: state.watchlist,
          portfolio: state.portfolio,
          lastUpdated: state.lastUpdated,
          profileLastUpdated: state.profileLastUpdated,
        }),
      }
    )
  )
);

// 选择器函数，用于优化组件订阅
export const userSelectors = {
  // 认证选择器
  getIsAuthenticated: () => (state: UserState & UserActions) =>
    state.isAuthenticated,
  getUser: () => (state: UserState & UserActions) => state.user,
  getToken: () => (state: UserState & UserActions) => state.token,
  getError: () => (state: UserState & UserActions) => state.error,

  // 用户数据选择器
  getWatchlist: () => (state: UserState & UserActions) => state.watchlist,
  getPortfolio: () => (state: UserState & UserActions) => state.portfolio,

  // 组合选择器
  getUserPreferences: () => (state: UserState & UserActions) =>
    state.user?.preferences,
  getPortfolioValue: () => (state: UserState & UserActions) => {
    return state.portfolio.reduce((total, item) => {
      return total + item.amount * item.purchasePrice;
    }, 0);
  },

  isInWatchlist: (cryptoId: string) => (state: UserState & UserActions) =>
    state.watchlist.includes(cryptoId),

  getPortfolioItem: (cryptoId: string) => (state: UserState & UserActions) =>
    state.portfolio.find(item => item.cryptoId === cryptoId),
};

// 导出加载状态 ID，供组件使用
export { USER_LOADING_IDS };

// 初始化用户store
export const initUserStore = () => {
  const { token, getUserProfile } = useUserStore.getState();

  // 如果有token，尝试获取用户资料
  if (token) {
    getUserProfile().catch(error => {
      console.error('Failed to init user profile:', error);
      // 如果获取失败，可能token已过期，清除认证状态
      useUserStore.getState().logout();
    });
  }
};
