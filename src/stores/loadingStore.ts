import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// 加载状态类型
export interface LoadingState {
  id: string;
  message?: string;
  progress?: number; // 0-100
  type: 'loading' | 'success' | 'error';
  startTime: number;
  duration?: number;
}

// 加载状态管理接口
interface LoadingStoreState {
  loadingStates: Map<string, LoadingState>;
  globalLoading: boolean;
  globalProgress: number;
}

interface LoadingStoreActions {
  startLoading: (id: string, message?: string) => void;
  updateProgress: (id: string, progress: number, message?: string) => void;
  finishLoading: (id: string, type?: 'success' | 'error') => void;
  clearLoading: (id: string) => void;
  clearAllLoading: () => void;
  isLoading: (id: string) => boolean;
  getLoadingState: (id: string) => LoadingState | undefined;
  getGlobalLoadingInfo: () => {
    isLoading: boolean;
    progress: number;
    activeCount: number;
  };
}

// 创建加载状态管理 store
export const useLoadingStore = create<LoadingStoreState & LoadingStoreActions>()(
  subscribeWithSelector((set, get) => ({
    loadingStates: new Map(),
    globalLoading: false,
    globalProgress: 0,

    startLoading: (id: string, message?: string) => {
      set(state => {
        const newStates = new Map(state.loadingStates);
        newStates.set(id, {
          id,
          message,
          progress: 0,
          type: 'loading',
          startTime: Date.now(),
        });

        const globalLoading = newStates.size > 0;
        const globalProgress = calculateGlobalProgress(newStates);

        return {
          loadingStates: newStates,
          globalLoading,
          globalProgress,
        };
      });
    },

    updateProgress: (id: string, progress: number, message?: string) => {
      set(state => {
        const newStates = new Map(state.loadingStates);
        const existing = newStates.get(id);
        
        if (existing) {
          newStates.set(id, {
            ...existing,
            progress: Math.max(0, Math.min(100, progress)),
            message: message || existing.message,
          });

          const globalProgress = calculateGlobalProgress(newStates);

          return {
            loadingStates: newStates,
            globalProgress,
          };
        }

        return state;
      });
    },

    finishLoading: (id: string, type: 'success' | 'error' = 'success') => {
      set(state => {
        const newStates = new Map(state.loadingStates);
        const existing = newStates.get(id);
        
        if (existing) {
          newStates.set(id, {
            ...existing,
            progress: 100,
            type,
            duration: Date.now() - existing.startTime,
          });

          // 延迟清除完成状态
          setTimeout(() => {
            get().clearLoading(id);
          }, 500);

          const globalLoading = Array.from(newStates.values()).some(
            state => state.type === 'loading'
          );
          const globalProgress = calculateGlobalProgress(newStates);

          return {
            loadingStates: newStates,
            globalLoading,
            globalProgress,
          };
        }

        return state;
      });
    },

    clearLoading: (id: string) => {
      set(state => {
        const newStates = new Map(state.loadingStates);
        newStates.delete(id);

        const globalLoading = newStates.size > 0;
        const globalProgress = calculateGlobalProgress(newStates);

        return {
          loadingStates: newStates,
          globalLoading,
          globalProgress,
        };
      });
    },

    clearAllLoading: () => {
      set({
        loadingStates: new Map(),
        globalLoading: false,
        globalProgress: 0,
      });
    },

    isLoading: (id: string) => {
      const state = get().loadingStates.get(id);
      return state?.type === 'loading';
    },

    getLoadingState: (id: string) => {
      return get().loadingStates.get(id);
    },

    getGlobalLoadingInfo: () => {
      const { loadingStates, globalLoading, globalProgress } = get();
      return {
        isLoading: globalLoading,
        progress: globalProgress,
        activeCount: loadingStates.size,
      };
    },
  }))
);

// 计算全局进度
function calculateGlobalProgress(states: Map<string, LoadingState>): number {
  if (states.size === 0) return 0;

  const totalProgress = Array.from(states.values()).reduce(
    (sum, state) => sum + (state.progress || 0),
    0
  );

  return Math.round(totalProgress / states.size);
}

// 选择器函数，用于优化组件订阅
export const loadingSelectors = {
  // 获取特定 ID 的加载状态
  getLoadingById: (id: string) => (state: LoadingStoreState & LoadingStoreActions) =>
    state.loadingStates.get(id),

  // 获取全局加载状态
  getGlobalLoading: () => (state: LoadingStoreState & LoadingStoreActions) =>
    state.globalLoading,

  // 获取全局进度
  getGlobalProgress: () => (state: LoadingStoreState & LoadingStoreActions) =>
    state.globalProgress,

  // 检查是否有任何加载状态
  hasAnyLoading: () => (state: LoadingStoreState & LoadingStoreActions) =>
    state.loadingStates.size > 0,

  // 获取加载状态数量
  getLoadingCount: () => (state: LoadingStoreState & LoadingStoreActions) =>
    state.loadingStates.size,
};

// Hook 用于简化组件中的使用
export const useLoading = (id: string) => {
  const startLoading = useLoadingStore(state => state.startLoading);
  const updateProgress = useLoadingStore(state => state.updateProgress);
  const finishLoading = useLoadingStore(state => state.finishLoading);
  const clearLoading = useLoadingStore(state => state.clearLoading);
  const isLoading = useLoadingStore(state => state.isLoading(id));
  const loadingState = useLoadingStore(state => state.getLoadingState(id));

  return {
    isLoading,
    loadingState,
    startLoading: (message?: string) => startLoading(id, message),
    updateProgress: (progress: number, message?: string) => 
      updateProgress(id, progress, message),
    finishLoading: (type?: 'success' | 'error') => finishLoading(id, type),
    clearLoading: () => clearLoading(id),
  };
};

// 全局加载状态 Hook
export const useGlobalLoading = () => {
  const globalLoading = useLoadingStore(loadingSelectors.getGlobalLoading());
  const globalProgress = useLoadingStore(loadingSelectors.getGlobalProgress());
  const loadingCount = useLoadingStore(loadingSelectors.getLoadingCount());

  return {
    isLoading: globalLoading,
    progress: globalProgress,
    activeCount: loadingCount,
  };
};