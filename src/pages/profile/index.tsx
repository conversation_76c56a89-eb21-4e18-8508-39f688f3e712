import { ScrollView, Text, View } from '@tarojs/components';
import React, { useEffect, useState } from 'react';
import CryptoElevator from '../../components/CryptoElevator/index';
import { CryptoItem } from '../../components/CryptoElevator/types';
import Container from '../../components/Layout/Container';
import { NavigationBar } from '../../components/Navigation';
import TabBar from '../../components/TabBar';
import { dataSelectors, useDataStore } from '../../stores/dataStore';
import {
  LanguageSelector,
  ProfileSkeleton,
  SubscriptionCard,
} from './components';
import './index.scss';

const Profile: React.FC = () => {
  const [showCryptoElevator, setShowCryptoElevator] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('zh-CN');

  // 从dataStore获取状态
  const selectedCoinsForProfile = useDataStore(
    dataSelectors.getSelectedCoinsForProfile()
  );
  const { setSelectedCoinsForProfile, searchCryptos } = useDataStore();

  // 初始化数据
  useEffect(() => {
    // 可以在这里初始化一些默认的关注币种
  }, []);

  // 处理电梯组件确认选择
  const handleElevatorConfirm = (coins: CryptoItem[]) => {
    setSelectedCoinsForProfile(coins);
  };

  // 打开电梯组件
  const handleOpenElevator = () => {
    setShowCryptoElevator(true);
    // 初始化搜索数据
    searchCryptos();
  };

  // 处理语言切换
  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    // 这里可以添加实际的语言切换逻辑
    console.log('切换语言到:', language);
  };

  return (
    <View className='profile-page'>
      <NavigationBar title='我的' />

      <Container>
        <ScrollView
          className='profile-content'
          scrollY
          enhanced
          showScrollbar={false}
        >
          {/* 个人信息区域 - 使用骨架屏 */}
          <View className='profile-section'>
            <ProfileSkeleton />
          </View>

          {/* 订阅信息卡片 */}
          <View className='profile-section'>
            <SubscriptionCard />
          </View>

          {/* 关注的crypto */}
          <View className='profile-section'>
            <View className='profile-item' onClick={handleOpenElevator}>
              <View className='profile-item__content'>
                <Text className='profile-item__label'>我的币种</Text>
                <Text className='profile-item__count'>
                  ({selectedCoinsForProfile.length}/4)
                </Text>
              </View>
              <Text className='profile-item__arrow'>›</Text>
            </View>

            {/* 显示已选择的币种 */}
            {selectedCoinsForProfile.length > 0 && (
              <View className='profile-coins'>
                {selectedCoinsForProfile.map(coin => (
                  <View key={coin.id} className='profile-coin-item'>
                    <Text className='profile-coin-name'>{coin.name}</Text>
                    <Text className='profile-coin-symbol'>({coin.symbol})</Text>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* 语言选择 */}
          <View className='profile-section'>
            <LanguageSelector
              selectedLanguage={selectedLanguage}
              onLanguageChange={handleLanguageChange}
            />
          </View>
        </ScrollView>
      </Container>

      {/* 电梯组件 */}
      <CryptoElevator
        visible={showCryptoElevator}
        onClose={() => setShowCryptoElevator(false)}
        onConfirm={handleElevatorConfirm}
        mode='multiple'
        maxSelection={4}
        title='我的币种'
        preSelectedCoins={selectedCoinsForProfile}
      />

      <TabBar current={2} />
    </View>
  );
};

export default Profile;
