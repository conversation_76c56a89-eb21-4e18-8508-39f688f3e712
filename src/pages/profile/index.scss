.profile-page {
  min-height: 100vh;
  background: var(--color-background);
  color: var(--color-text-primary);
  padding-bottom: 5rem; // 为TabBar留出空间
}

.profile-content {
  height: calc(100vh - 8rem); // 减去导航栏和TabBar高度
  padding: 1rem;
}

.profile-section {
  margin-bottom: 1rem;
}

// 个人信息项
.profile-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--color-surface);
  border-radius: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--color-surface-hover, #f5f5f5);
  }

  &__content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  &__label {
    font-size: 1rem;
    font-weight: 500;
    color: var(--color-text-primary);
    line-height: 1.5;
    display: flex;
    align-items: center;
  }

  &__count {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    line-height: 1.5;
    display: flex;
    align-items: center;
  }

  &__arrow {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 币种列表
.profile-coins {
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.profile-coin-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  background: var(--color-primary, #007aff);
  border-radius: 1rem;
  font-size: 0.75rem;

  .profile-coin-name {
    color: white;
    font-weight: 500;
    line-height: 1.5;
    display: flex;
    align-items: center;
  }

  .profile-coin-symbol {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    display: flex;
    align-items: center;
  }
}

// 个人信息骨架屏
.profile-skeleton {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: var(--color-surface);
  border-radius: 0.8rem;
  box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.1);

  &__avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    margin-right: 1rem;
  }

  &__info {
    flex: 1;
  }

  &__name {
    width: 6rem;
    height: 1rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 0.2rem;
    margin-bottom: 0.5rem;
  }

  &__subtitle {
    width: 8rem;
    height: 0.8rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 0.2rem;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 订阅卡片
.subscription-card {
  background: var(--color-surface);
  border-radius: 0.8rem;
  padding: 1.5rem;
  box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.1);

  &__header {
    margin-bottom: 1rem;
  }

  &__title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  &__badges {
    display: flex;
    gap: 0.5rem;
  }

  &__stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
}

.subscription-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.7rem;
  font-weight: 500;

  &--primary {
    background: #e3f2fd;
    color: #1976d2;
  }

  &--secondary {
    background: #f3e5f5;
    color: #7b1fa2;
  }

  &__text {
    font-size: 0.7rem;
  }
}

.subscription-stat {
  display: flex;
  align-items: center;
  font-size: 0.8rem;

  &__label {
    color: var(--color-text-secondary);
    margin-right: 0.5rem;
  }

  &__value {
    color: var(--color-text-primary);
    font-weight: 600;
  }
}

// 通用列表项
.profile-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: var(--color-surface);
  border-radius: 0.8rem;
  box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    background: var(--color-border);
  }

  &__label {
    font-size: 0.9rem;
    color: var(--color-text-primary);
  }

  &__arrow {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    font-weight: 300;
  }
}

// 语言选择器
.language-selector {
  position: relative;
  background: var(--color-surface);
  border-radius: 0.8rem;
  box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.1);

  &__trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    cursor: pointer;
  }

  &__label {
    font-size: 0.9rem;
    color: var(--color-text-primary);
  }

  &__value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  &__text {
    font-size: 0.8rem;
    color: var(--color-text-secondary);
  }

  &__arrow {
    font-size: 0.7rem;
    color: var(--color-text-secondary);
    transition: transform 0.2s ease;
  }

  &__dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--color-surface);
    border-radius: 0 0 0.8rem 0.8rem;
    box-shadow: 0 0.2rem 0.8rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
  }

  &__option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.8rem 1.5rem;
    cursor: pointer;
    transition: background 0.2s ease;

    &:hover {
      background: var(--color-border);
    }

    &--selected {
      background: rgba(25, 118, 210, 0.1);
    }
  }

  &__option-text {
    font-size: 0.8rem;
    color: var(--color-text-primary);
  }

  &__check {
    font-size: 0.8rem;
    color: var(--color-primary);
    font-weight: 600;
  }
}

// 加密货币抽屉
.crypto-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;

  &__mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    animation: fade-in 0.3s ease;
  }

  &__content {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20rem;
    max-width: 100vw;
    background: var(--color-surface);
    box-shadow: -0.2rem 0 1rem rgba(0, 0, 0, 0.15);
    animation: slide-in-right 0.3s ease;
    display: flex;
    flex-direction: column;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 0.05rem solid var(--color-border);
    background: var(--color-surface);
  }

  &__back {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    transition: background 0.2s ease;

    &:hover {
      background: var(--color-border);
    }
  }

  &__back-icon {
    font-size: 1.5rem;
    color: var(--color-text-primary);
    font-weight: 300;
  }

  &__title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  &__placeholder {
    width: 2rem;
  }

  &__body {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
  }
}

// 加密货币网格
.crypto-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.crypto-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: var(--color-background);
  border: 0.05rem solid var(--color-border);
  border-radius: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    transform: translateY(-0.1rem);
    box-shadow: 0 0.2rem 0.8rem rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }

  &__icon {
    margin-bottom: 0.5rem;
  }

  &__avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &__name {
    font-size: 0.8rem;
    color: var(--color-text-primary);
    text-align: center;
    margin-bottom: 0.5rem;
  }

  &__check {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
  }

  &__checkbox {
    width: 1.2rem;
    height: 1.2rem;
    border: 0.1rem solid var(--color-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-surface);
    transition: all 0.2s ease;

    &--checked {
      background: var(--color-primary);
      border-color: var(--color-primary);
    }
  }

  &__check-icon {
    font-size: 0.7rem;
    color: #fff;
    font-weight: 600;
  }
}

// 动画
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

// 响应式设计
@media (max-width: 30rem) {
  .crypto-drawer__content {
    width: 100vw;
  }

  .crypto-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }

  .crypto-item {
    padding: 0.8rem;

    &__avatar {
      width: 2rem;
      height: 2rem;
    }

    &__name {
      font-size: 0.7rem;
    }

    &__checkbox {
      width: 1rem;
      height: 1rem;
    }

    &__check-icon {
      font-size: 0.6rem;
    }
  }
}
