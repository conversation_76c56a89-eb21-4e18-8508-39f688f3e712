import { View, Text } from '@tarojs/components';
import React, { useState } from 'react';

interface LanguageSelectorProps {
  selectedLanguage: string;
  onLanguageChange: (language: string) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  selectedLanguage,
  onLanguageChange,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const languages = [
    { code: 'zh-CN', name: '中文简体' },
    { code: 'zh-TW', name: '中文繁體' },
    { code: 'en-US', name: 'English' },
    { code: 'ja-<PERSON>', name: '日本語' },
    { code: 'ko-KR', name: '한국어' },
  ];

  const selectedLang = languages.find(lang => lang.code === selectedLanguage);

  const handleLanguageSelect = (languageCode: string) => {
    onLanguageChange(languageCode);
    setShowDropdown(false);
  };

  return (
    <View className='language-selector'>
      <View
        className='language-selector__trigger'
        onClick={() => setShowDropdown(!showDropdown)}
      >
        <Text className='language-selector__label'>语言</Text>
        <View className='language-selector__value'>
          <Text className='language-selector__text'>
            {selectedLang?.name || '中文简体'}
          </Text>
          <Text className='language-selector__arrow'>
            {showDropdown ? '▲' : '▼'}
          </Text>
        </View>
      </View>

      {showDropdown && (
        <View className='language-selector__dropdown'>
          {languages.map(language => (
            <View
              key={language.code}
              className={`language-selector__option ${
                selectedLanguage === language.code
                  ? 'language-selector__option--selected'
                  : ''
              }`}
              onClick={() => handleLanguageSelect(language.code)}
            >
              <Text className='language-selector__option-text'>
                {language.name}
              </Text>
              {selectedLanguage === language.code && (
                <Text className='language-selector__check'>✓</Text>
              )}
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

export default LanguageSelector;
