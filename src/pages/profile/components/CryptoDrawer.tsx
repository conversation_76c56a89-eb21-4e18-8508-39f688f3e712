import { View, Text } from '@tarojs/components';
import React from 'react';

interface Crypto {
  id: string;
  name: string;
  symbol: string;
  followed: boolean;
}

interface CryptoDrawerProps {
  visible: boolean;
  cryptos: Crypto[];
  onClose: () => void;
  onCryptoToggle: (cryptoId: string) => void;
}

const CryptoDrawer: React.FC<CryptoDrawerProps> = ({
  visible,
  cryptos,
  onClose,
  onCryptoToggle,
}) => {
  if (!visible) return null;

  return (
    <View className='crypto-drawer'>
      {/* 遮罩层 */}
      <View className='crypto-drawer__mask' onClick={onClose}></View>
      
      {/* 抽屉内容 */}
      <View className='crypto-drawer__content'>
        <View className='crypto-drawer__header'>
          <View className='crypto-drawer__back' onClick={onClose}>
            <Text className='crypto-drawer__back-icon'>‹</Text>
          </View>
          <Text className='crypto-drawer__title'>cryptoList</Text>
          <View className='crypto-drawer__placeholder'></View>
        </View>

        <View className='crypto-drawer__body'>
          <View className='crypto-grid'>
            {cryptos.map(crypto => (
              <View
                key={crypto.id}
                className='crypto-item'
                onClick={() => onCryptoToggle(crypto.id)}
              >
                <View className='crypto-item__icon'>
                  <View className='crypto-item__avatar'></View>
                </View>
                <Text className='crypto-item__name'>{crypto.name}</Text>
                <View className='crypto-item__check'>
                  <View
                    className={`crypto-item__checkbox ${
                      crypto.followed ? 'crypto-item__checkbox--checked' : ''
                    }`}
                  >
                    {crypto.followed && (
                      <Text className='crypto-item__check-icon'>✓</Text>
                    )}
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>
      </View>
    </View>
  );
};

export default CryptoDrawer;
