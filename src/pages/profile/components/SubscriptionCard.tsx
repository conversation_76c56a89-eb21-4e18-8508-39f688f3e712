import { View, Text } from '@tarojs/components';
import React from 'react';

const SubscriptionCard: React.FC = () => {
  return (
    <View className='subscription-card'>
      <View className='subscription-card__header'>
        <Text className='subscription-card__title'>订阅</Text>
      </View>
      
      <View className='subscription-card__content'>
        <View className='subscription-card__badges'>
          <View className='subscription-badge subscription-badge--primary'>
            <Text className='subscription-badge__text'>免广告</Text>
          </View>
          <View className='subscription-badge subscription-badge--secondary'>
            <Text className='subscription-badge__text'>使用次数</Text>
          </View>
        </View>
        
        <View className='subscription-card__stats'>
          <View className='subscription-stat'>
            <Text className='subscription-stat__label'>剩余请求次数：</Text>
            <Text className='subscription-stat__value'>99</Text>
          </View>
          
          <View className='subscription-stat'>
            <Text className='subscription-stat__label'>有效期：</Text>
            <Text className='subscription-stat__value'>2026-03-27</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default SubscriptionCard;
