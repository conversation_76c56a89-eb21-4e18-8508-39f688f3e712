import { ScrollView, Text, View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import React, { useEffect, useState } from 'react';
import Container from '../../components/Layout/Container';
import { InlineLoading } from '../../components/LoadingIndicator';
import { NavigationBar } from '../../components/Navigation';
import { useNewsStore } from '../../stores/news-fetch';
import { NewsItemType } from '../../types';
import './index.scss';

const NewsDetails: React.FC = () => {
  const router = useRouter();
  const [newsDetail, setNewsDetail] = useState<NewsItemType | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchNewsDetail = useNewsStore(state => state.fetchNewsDetail);

  useEffect(() => {
    const loadNewsDetail = async () => {
      try {
        setLoading(true);
        // 从路由参数中获取新闻URL
        const { url } = router.params;

        if (url) {
          const detail = await fetchNewsDetail(decodeURIComponent(url));
          setNewsDetail(detail);
        } else {
          setError('缺少新闻链接参数');
        }
      } catch (err) {
        setError('获取新闻详情失败');
        console.error('获取新闻详情失败:', err);
      } finally {
        setLoading(false);
      }
    };

    loadNewsDetail();
  }, [fetchNewsDetail, router.params]);

  return (
    <>
      <NavigationBar
        title={newsDetail?.title || '新闻详情'}
        showBack={true}
        showThemeToggle={false}
      />

      <ScrollView className='news-details-page' scrollY>
        <Container maxWidth='xl' padding>
          {loading ? (
            <View className='loading-container'>
              <InlineLoading text='加载新闻详情...' />
            </View>
          ) : error ? (
            <View className='error-container'>
              <Text className='error-message'>{error}</Text>
            </View>
          ) : newsDetail ? (
            <View className='news-details-content'>
              <View className='news-header'>
                <Text className='news-title'>{newsDetail.title}</Text>
                <View className='news-meta'>
                  <Text className='news-source'>{newsDetail.source}</Text>
                  <Text className='news-date'>
                    {new Date(newsDetail.publishedAt).toLocaleDateString()}
                  </Text>
                </View>
              </View>

              {newsDetail.imageUrl && (
                <View className='news-image-container'>
                  <img
                    src={newsDetail.imageUrl}
                    alt={newsDetail.title}
                    className='news-image'
                  />
                </View>
              )}

              <View className='news-body'>
                {newsDetail.content ? (
                  <Text className='news-content'>{newsDetail.content}</Text>
                ) : (
                  <Text className='news-summary'>{newsDetail.description}</Text>
                )}
              </View>
            </View>
          ) : (
            <View className='no-data'>
              <Text>暂无新闻详情</Text>
            </View>
          )}
        </Container>
      </ScrollView>
    </>
  );
};

export default NewsDetails;
