import { Button, Checkbox, Input, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useState } from 'react';
import Container from '../../components/Layout/Container';
import ResponsiveText from '../../components/Layout/ResponsiveText';
import { InlineLoading } from '../../components/LoadingIndicator';
import { NavigationBar } from '../../components/Navigation';
import { useUserStore } from '../../stores/userStore';
import { platform } from '../../utils/platform';
import './index.scss';

interface AuthFormData {
  email: string; // 邮箱
  password: string;
  confirmPassword?: string;
  agreeTerms: boolean;
}

const Auth: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState<AuthFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const { login, register, wechatLogin, error: authError } = useUserStore();

  // 验证邮箱格式
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = '请输入邮箱';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = '请输入正确的邮箱格式';
    }

    if (!formData.password.trim()) {
      newErrors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少6位';
    }

    if (!isLogin) {
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = '请确认密码';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = '两次密码输入不一致';
      }

      if (!formData.agreeTerms) {
        newErrors.agreeTerms = '请同意服务条款和隐私政策';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 微信登录
  const handleWechatLogin = async () => {
    if (!platform.isMiniProgram()) {
      Taro.showToast({
        title: '微信登录仅支持小程序环境',
        icon: 'none',
      });
      return;
    }

    try {
      setIsLoading(true);

      // 获取微信登录凭证
      const loginRes = await Taro.login();

      if (loginRes.code) {
        // 获取用户信息
        const userInfoRes = await Taro.getUserProfile({
          desc: '用于完善用户资料',
        });

        // 调用store中的微信登录方法
        await wechatLogin({
          code: loginRes.code,
          userInfo: userInfoRes.userInfo,
        });

        Taro.showToast({
          title: '微信登录成功',
          icon: 'success',
        });

        // 跳转到首页
        setTimeout(() => {
          Taro.switchTab({
            url: '/pages/index/index',
          });
        }, 1500);
      }
    } catch (error: any) {
      console.error('微信登录失败:', error);
      Taro.showToast({
        title: error.message || '微信登录失败',
        icon: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理登录
  const handleLogin = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      await login({
        username: formData.email,
        password: formData.password,
      });

      Taro.showToast({
        title: '登录成功',
        icon: 'success',
      });

      // 跳转到首页
      setTimeout(() => {
        Taro.switchTab({
          url: '/pages/index/index',
        });
      }, 1500);
    } catch (error: any) {
      console.error('登录失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理注册
  const handleRegister = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      await register({
        username: formData.email,
        email: formData.email,
        password: formData.password,
      });

      Taro.showToast({
        title: '注册成功',
        icon: 'success',
      });

      // 跳转到首页
      setTimeout(() => {
        Taro.switchTab({
          url: '/pages/index/index',
        });
      }, 1500);
    } catch (error: any) {
      console.error('注册失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 切换登录/注册模式
  const toggleMode = () => {
    setIsLogin(!isLogin);
    setErrors({});
    setFormData({
      ...formData,
      confirmPassword: '',
      agreeTerms: false,
    });
  };

  return (
    <View className='auth-page'>
      <NavigationBar title={isLogin ? '登录' : '注册'} />

      <Container maxWidth='sm' padding>
        <View className='auth-container'>
          {/* 头部标题 */}
          <View className='auth-header'>
            <ResponsiveText
              variant='h4'
              weight='bold'
              className='welcome-title'
            >
              Hello
            </ResponsiveText>
            <ResponsiveText
              variant='h5'
              weight='medium'
              className='welcome-subtitle'
            >
              欢迎使用Cryptocurrency
            </ResponsiveText>
          </View>

          {/* 表单区域 */}
          <View className='auth-form'>
            {/* 邮箱输入 */}
            <View className='form-group'>
              <Input
                className={`form-input ${errors.email ? 'error' : ''}`}
                placeholder='请输入邮箱或手机号'
                value={formData.email}
                onInput={e =>
                  setFormData({ ...formData, email: e.detail.value })
                }
              />
              {errors.email && (
                <Text className='error-text'>{errors.email}</Text>
              )}
            </View>

            {/* 密码输入 */}
            <View className='form-group'>
              <View className='password-input-wrapper'>
                <Input
                  className={`form-input ${errors.password ? 'error' : ''}`}
                  placeholder='请输入密码'
                  password={!showPassword}
                  value={formData.password}
                  onInput={e =>
                    setFormData({ ...formData, password: e.detail.value })
                  }
                />
                <View
                  className='password-toggle'
                  onClick={() => setShowPassword(!showPassword)}
                >
                  <Text className='toggle-icon'>
                    {showPassword ? '👁️' : '👁️‍🗨️'}
                  </Text>
                </View>
              </View>
              {errors.password && (
                <Text className='error-text'>{errors.password}</Text>
              )}
            </View>

            {/* 注册时的确认密码 */}
            {!isLogin && (
              <View className='form-group'>
                <Input
                  className={`form-input ${
                    errors.confirmPassword ? 'error' : ''
                  }`}
                  placeholder='请确认密码'
                  password={!showPassword}
                  value={formData.confirmPassword}
                  onInput={e =>
                    setFormData({
                      ...formData,
                      confirmPassword: e.detail.value,
                    })
                  }
                />
                {errors.confirmPassword && (
                  <Text className='error-text'>{errors.confirmPassword}</Text>
                )}
              </View>
            )}

            {/* 注册时的服务条款 */}
            {!isLogin && (
              <View className='form-group'>
                <View className='checkbox-wrapper'>
                  <Checkbox
                    value='agree'
                    checked={formData.agreeTerms}
                    onChange={e =>
                      setFormData({
                        ...formData,
                        agreeTerms: e.detail.value.includes('agree'),
                      })
                    }
                  />
                  <Text className='checkbox-text'>
                    我已阅读并同意
                    <Text className='link-text'>服务条款</Text>和
                    <Text className='link-text'>隐私政策</Text>
                  </Text>
                </View>
                {errors.agreeTerms && (
                  <Text className='error-text'>{errors.agreeTerms}</Text>
                )}
              </View>
            )}

            {/* 登录按钮 */}
            <View className='form-group'>
              <Button
                className='auth-button primary'
                onClick={isLogin ? handleLogin : handleRegister}
                disabled={isLoading}
              >
                {isLoading ? (
                  <InlineLoading
                    text={isLogin ? '登录中...' : '注册中...'}
                    size='small'
                  />
                ) : isLogin ? (
                  '登录'
                ) : (
                  '注册'
                )}
              </Button>
            </View>

            {/* 切换登录/注册 */}
            <View className='mode-switch'>
              <Text className='switch-text'>
                {isLogin ? '还没有账号？' : '已有账号？'}
                <Text className='link-text' onClick={toggleMode}>
                  {isLogin ? '立即注册' : '立即登录'}
                </Text>
              </Text>
            </View>

            {/* 第三方登录分割线 */}
            <View className='divider'>
              <View className='divider-line'></View>
              <Text className='divider-text'>或者</Text>
              <View className='divider-line'></View>
            </View>

            {/* 微信登录 */}
            <View className='third-party-login'>
              <Button
                className='wechat-login-button'
                onClick={handleWechatLogin}
                disabled={isLoading}
              >
                <View className='wechat-icon'>
                  <Text className='wechat-emoji'>💬</Text>
                </View>
              </Button>
            </View>

            {/* 错误提示 */}
            {authError && (
              <View className='error-message'>
                <Text className='error-text'>{authError}</Text>
              </View>
            )}
          </View>
        </View>
      </Container>
    </View>
  );
};

export default Auth;
