// 登录注册页面样式
.auth-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  // 暗色主题适配
  [data-theme='dark'] & {
    background: #1a1a1a;
  }
}

.auth-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 2rem 1.5rem;
  min-height: calc(100vh - 88px); // 减去导航栏高度
  max-width: 400px;
  margin: 0 auto;
}

// 头部标题区域
.auth-header {
  text-align: left;
  margin-bottom: 3rem;
  padding: 2rem 0 1rem 0;

  .welcome-title {
    color: #333333;
    margin-bottom: 0.5rem;
    font-size: 2rem;
    font-weight: 600;
  }

  .welcome-subtitle {
    color: #333333;
    font-size: 1.125rem;
    font-weight: 400;
  }

  // 暗色主题适配
  [data-theme='dark'] & {
    .welcome-title,
    .welcome-subtitle {
      color: #ffffff;
    }
  }
}

// 表单区域
.auth-form {
  background: transparent;
  padding: 0;

  // 暗色主题适配
  [data-theme='dark'] & {
    background: transparent;
  }
}

// 表单组
.form-group {
  margin-bottom: 1.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

// 输入框样式
.form-input {
  width: 100%;
  height: 3.5rem;
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  text-align: left;
  padding: 0 1rem;
  line-height: 3.5rem;
  display: flex;
  align-items: center;
  font-size: 1rem;
  color: #333333;
  text-align: center; // 文字居中对齐
  transition: all 0.2s ease;

  &::placeholder {
    color: #999999;
    text-align: center; // 占位符文字也居中
  }

  &:focus {
    border-color: #007aff;
    background: #ffffff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
  }

  &.error {
    border-color: #ff3b30;
    background: #fff5f5;
  }

  // 暗色主题适配
  [data-theme='dark'] & {
    background: #2d2d2d;
    border-color: #404040;
    color: #ffffff;

    &::placeholder {
      color: #888888;
    }

    &:focus {
      background: #1a1a1a;
      border-color: #007aff;
    }

    &.error {
      border-color: #ff3b30;
      background: #2d1a1a;
    }
  }
}

// 密码输入框包装器
.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;

  .password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }

    .toggle-icon {
      font-size: 1.25rem;
      color: #718096;
    }
  }
}

// 复选框包装器
.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;

  .checkbox-text {
    font-size: 0.875rem;
    color: #4a5568;
    line-height: 1.7rem;

    // 暗色主题适配
    [data-theme='dark'] & {
      color: #e2e8f0;
    }
  }
}

// 链接文本
.link-text {
  color: #007aff;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #0056cc;
    text-decoration: underline;
  }

  // 暗色主题适配
  [data-theme='dark'] & {
    color: #007aff;

    &:hover {
      color: #0056cc;
    }
  }
}

// 错误文本
.error-text {
  color: #e53e3e;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: block;
}

// 认证按钮
.auth-button {
  width: 100%;
  height: 3.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &.primary {
    background: #6c7ce7;
    color: #ffffff;

    &:hover {
      background: #5a6fd8;
      transform: translateY(-1px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      background: #6c7ce7;
    }
  }
}

// 认证链接
.auth-links {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

// 模式切换
.mode-switch {
  text-align: center;
  margin-bottom: 2rem;

  .switch-text {
    font-size: 0.875rem;
    color: #718096;

    // 暗色主题适配
    [data-theme='dark'] & {
      color: #a0aec0;
    }
  }
}

// 分割线
.divider {
  display: flex;
  align-items: center;
  margin: 3rem 0 2rem 0;

  .divider-line {
    flex: 1;
    height: 1px;
    background: #e0e0e0;

    // 暗色主题适配
    [data-theme='dark'] & {
      background: #404040;
    }
  }

  .divider-text {
    padding: 0 1rem;
    font-size: 0.875rem;
    color: #999999;
    background: #f5f5f5;

    // 暗色主题适配
    [data-theme='dark'] & {
      color: #888888;
      background: #1a1a1a;
    }
  }
}

// 第三方登录
.third-party-login {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

// 微信登录按钮
.wechat-login-button {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background: #07c160;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(7, 193, 96, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .wechat-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    .wechat-emoji {
      font-size: 1.5rem;
      color: #ffffff;
    }
  }
}

// 错误消息
.error-message {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-top: 1rem;

  // 暗色主题适配
  [data-theme='dark'] & {
    background: #742a2a;
    border-color: #c53030;
  }
}

// 响应式设计
@media (max-width: 640px) {
  .auth-container {
    padding: 1rem 1rem;
    min-height: calc(100vh - 88px);
  }

  .auth-header {
    margin-bottom: 2rem;
    padding: 1rem 0;

    .welcome-title {
      font-size: 1.75rem;
    }

    .welcome-subtitle {
      font-size: 1rem;
    }
  }

  .form-input {
    height: 3rem;
    line-height: 3rem;
    font-size: 0.875rem;
    text-align: left;
  }

  .auth-button {
    height: 3rem;
    font-size: 0.875rem;
  }

  .wechat-login-button {
    width: 3rem;
    height: 3rem;

    .wechat-emoji {
      font-size: 1.25rem;
    }
  }
}
