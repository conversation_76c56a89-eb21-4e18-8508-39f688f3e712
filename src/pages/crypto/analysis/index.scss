.analysis-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    text-align: center;
    margin-bottom: 30px;

    .page-title {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .page-subtitle {
      font-size: 14px;
      color: #666;
    }
  }

  .error-banner {
    background-color: #fee;
    border: 1px solid #fcc;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 20px;
    text-align: center;
    color: #c33;
  }

  .coin-selection {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .selection-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .coin-list {
      max-height: 300px;
      overflow-y: auto;

      .coin-item {
        padding: 12px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .coin-info {
          font-size: 14px;
          color: #333;
          margin-left: 8px;
        }
      }
    }
  }

  .action-section {
    margin-bottom: 30px;
    text-align: center;
  }

  .results-section {
    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      display: block;
    }

    .analysis-result {
      background-color: white;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #eee;

        .coin-name {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }

        .status {
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: bold;

          &.success {
            background-color: #e8f5e8;
            color: #4caf50;
          }

          &.error {
            background-color: #fee;
            color: #f44336;
          }
        }
      }

      .result-content {
        .price-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;
          padding: 12px;
          background-color: #f8f9fa;
          border-radius: 8px;

          .positive {
            color: #4caf50;
          }

          .negative {
            color: #f44336;
          }
        }

        .news-info {
          margin-bottom: 12px;
          font-size: 14px;
          color: #666;
        }

        .sentiment {
          margin-bottom: 16px;
          padding: 12px;
          background-color: #f0f8ff;
          border-radius: 8px;
          display: flex;
          justify-content: space-between;
        }

        .section-title {
          font-size: 14px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
          display: block;
        }

        .ai-analysis {
          margin-bottom: 16px;

          .analysis-text {
            font-size: 14px;
            line-height: 1.6;
            color: #555;
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            white-space: pre-wrap;
          }
        }

        .strategy {
          margin-bottom: 16px;
          padding: 12px;
          background-color: #e8f5e8;
          border-radius: 8px;
          border-left: 4px solid #4caf50;
        }

        .risk-warning {
          padding: 12px;
          background-color: #fff3cd;
          border-radius: 8px;
          border-left: 4px solid #ffc107;

          .warning-text {
            color: #856404;
            font-weight: 500;
          }
        }
      }

      .error-content {
        .error-message {
          color: #f44336;
          font-size: 14px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .analysis-page {
    padding: 16px;

    .coin-selection {
      padding: 16px;

      .selection-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
    }

    .analysis-result {
      padding: 16px;

      .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .price-info {
        flex-direction: column;
        gap: 8px;
      }
    }
  }
}
