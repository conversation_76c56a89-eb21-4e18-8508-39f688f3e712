import { Button } from '@nutui/nutui-react-taro';
import { ScrollView, Text, View } from '@tarojs/components';
import React from 'react';
import Container from '../../components/Layout/Container';
import ResponsiveText from '../../components/Layout/ResponsiveText';
import { NavigationBar } from '../../components/Navigation';
import './index.scss';

const Crypto: React.FC = () => {
  const handleSearchClick = () => {
    console.log('搜索按钮被点击');
  };

  const handleFilterClick = () => {
    console.log('筛选按钮被点击');
  };

  // 自定义右侧插槽内容
  const rightSlot = (
    <View className='crypto-nav-actions'>
      <Button
        size='small'
        fill='none'
        onClick={handleSearchClick}
        style={{ marginRight: '8px' }}
      >
        🔍
      </Button>
      <Button size='small' fill='none' onClick={handleFilterClick}>
        📊
      </Button>
    </View>
  );

  return (
    <>
      <NavigationBar
        title='💰 加密货币'
        showBack={true}
        showThemeToggle={false}
        rightSlot={rightSlot}
        onRightClick={handleSearchClick}
      />
      <ScrollView className='crypto-page' scrollY>
        <Container maxWidth='xl' padding>
          <View className='content-section'>
            <ResponsiveText variant='h6' weight='bold' color='primary'>
              加密货币行情
            </ResponsiveText>
            <ResponsiveText variant='body2' color='textSecondary'>
              这是一个示例页面，展示了导航栏的插槽功能
            </ResponsiveText>

            <View className='demo-info'>
              <Text>• 左侧：返回按钮</Text>
              <Text>• 中间：页面标题</Text>
              <Text>• 右侧：自定义插槽（搜索和筛选按钮）</Text>
              <Text>• 主题切换功能被禁用，使用自定义插槽</Text>
            </View>
          </View>
        </Container>
      </ScrollView>
    </>
  );
};

export default Crypto;
