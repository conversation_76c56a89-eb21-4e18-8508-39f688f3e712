.crypto-page {
  height: 100vh;
  background: var(--color-background, #ffffff);

  .content-section {
    padding: 32px 0;

    .demo-info {
      margin-top: 32px;
      padding: 24px;
      background: var(--color-surface, #f5f5f5);
      border-radius: 12px;
      border: 1px solid var(--color-border, #e0e0e0);

      text {
        display: block;
        margin-bottom: 8px;
        color: var(--color-text-secondary, #757575);
        font-size: 28px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.crypto-nav-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 小程序适配 */
@media screen and (max-width: 750px) {
  .crypto-page {
    .content-section {
      padding: 24px 0;

      .demo-info {
        margin-top: 24px;
        padding: 16px;

        text {
          font-size: 24px;
        }
      }
    }
  }
}

/* H5适配 */
@media screen and (min-width: 751px) {
  .crypto-page {
    .content-section {
      padding: 24px 0;

      .demo-info {
        margin-top: 24px;
        padding: 16px;

        text {
          font-size: 14px;
        }
      }
    }
  }
}