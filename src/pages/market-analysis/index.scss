.market-analysis {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;

  &__content {
    flex: 1;
    padding: 1rem;
  }

  &__form-item {
    margin-bottom: 1.2rem;
  }

  &__label {
    display: block;
    font-size: 0.8rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.4rem;
  }

  &__selector {
    width: 100%;
    min-height: 2.4rem;
    background-color: #fff;
    border: 0.05rem solid #e0e0e0;
    border-radius: 0.4rem;
    padding: 0.6rem 0.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.1);
  }

  &__selector-text {
    font-size: 0.7rem;
    color: #333;
    flex: 1;
  }

  &__selector-arrow {
    font-size: 0.6rem;
    color: #999;
  }

  &__selector--placeholder {
    color: #999;
    font-style: italic;
    cursor: pointer;
  }

  &__multi-selector {
    width: 100%;
    background-color: #fff;
    border: 0.05rem solid #e0e0e0;
    border-radius: 0.4rem;
    padding: 0.6rem 0.8rem;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }

  &__selector-label {
    font-size: 0.7rem;
    color: #333;
    margin-bottom: 0.4rem;
    font-weight: 500;
  }

  &__placeholder {
    color: #999;
    font-size: 0.7rem;
    font-style: italic;
  }

  // 下拉选择器样式
  &__dropdown-container {
    position: relative;
    width: 100%;
  }

  &__search-container {
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 0.05rem solid #e0e0e0;
    border-radius: 0.4rem;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.1);
  }

  &__search-input {
    flex: 1;
    padding: 0.6rem 0.8rem;
    font-size: 0.7rem;
    border: none;
    outline: none;
    background: transparent;
  }

  &__search-toggle {
    padding: 0.6rem 0.8rem;
    color: #999;
    cursor: pointer;
    user-select: none;
    border-left: 0.05rem solid #e0e0e0;
  }

  &__dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #fff;
    border: 0.05rem solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 0.4rem 0.4rem;
    box-shadow: 0 0.2rem 0.4rem rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  &__dropdown-scroll {
    max-height: 10rem;
  }

  &__dropdown-item {
    padding: 0.6rem 0.8rem;
    border-bottom: 0.05rem solid #f0f0f0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      background-color: #f8f9fa;
    }

    &--selected {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    &--loading,
    &--empty {
      color: #999;
      font-style: italic;
      text-align: center;
      cursor: default;

      &:hover {
        background-color: transparent;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }

  &__coin-info {
    display: flex;
    align-items: center;
    gap: 0.4rem;
  }

  &__coin-name {
    font-weight: 500;
    color: #333;
  }

  &__coin-symbol {
    color: #666;
    font-size: 0.6rem;
  }

  &__coin-selected {
    color: #4caf50;
    font-weight: bold;
  }

  &__selected-tags {
    margin-top: 0.4rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
  }

  &__tag {
    display: flex;
    align-items: center;
    background-color: #e3f2fd;
    border: 0.05rem solid #2196f3;
    border-radius: 0.8rem;
    padding: 0.2rem 0.6rem;
    font-size: 0.6rem;
  }

  &__tag-text {
    color: #1976d2;
    margin-right: 0.2rem;
  }

  &__tag-close {
    color: #1976d2;
    font-weight: bold;
    cursor: pointer;
    font-size: 0.7rem;
    line-height: 1;
  }

  &__add-block {
    margin: 1.6rem 0;
    text-align: center;
  }

  &__add-btn {
    background-color: rgba(255, 17, 17, 1);
    color: #fff;
    border: none;
    border-radius: 1.8rem;
    padding: 0.6rem 1.6rem;
    font-size: 0.8rem;
    font-weight: 500;
    min-width: 6rem;

    &:disabled {
      background-color: #ccc;
      color: #999;
    }
  }

  &__description {
    margin: 1.6rem 0;
    text-align: center;
  }

  &__description-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.6rem;
  }

  &__description-text {
    font-size: 0.7rem;
    color: #666;
    line-height: 1.6;
    padding: 0 0.8rem;
  }

  &__result {
    margin-top: 1.2rem;
    background-color: #fff;
    border-radius: 0.4rem;
    padding: 0.8rem;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.1);
  }

  &__result-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.6rem;
  }

  &__result-content {
    background-color: #f5f5f5;
    border-radius: 0.2rem;
    padding: 0.6rem;
    font-family: monospace;
    font-size: 0.6rem;
    color: #333;
    white-space: pre-wrap;
    word-break: break-all;
  }

  &__error {
    margin-top: 0.8rem;
    padding: 0.6rem;
    background-color: #ffebee;
    border: 0.05rem solid #f44336;
    border-radius: 0.2rem;
    color: #d32f2f;
    font-size: 0.7rem;
  }

  &__analyze-section {
    padding: 1rem;
    background-color: #fff;
    border-top: 0.05rem solid #e0e0e0;
    margin-top: 1rem;
  }

  &__analyze-btn {
    width: 100%;
    height: 2.4rem;
    background: linear-gradient(135deg, rgba(255, 17, 17, 1) 0%, #ff4444 100%);
    color: #fff;
    border: none;
    border-radius: 1.2rem;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0.2rem 0.6rem rgba(255, 17, 17, 0.3);

    &:hover {
      transform: translateY(-0.1rem);
      box-shadow: 0 0.3rem 0.8rem rgba(255, 17, 17, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled {
      background: #ccc;
      color: #999;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }
  }

  // 电梯组件相关样式
  &__input-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  &__coin-input {
    width: 100%;
    min-height: 2.4rem;
    background-color: #fff;
    border: 0.05rem solid #e0e0e0;
    border-radius: 0.4rem;
    padding: 0.6rem 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.1);

    &:hover {
      border-color: #007aff;
    }

    &:focus {
      border-color: #007aff;
      box-shadow: 0 0 0 0.125rem rgba(0, 122, 255, 0.1);
    }
  }

  &__coin-input-text {
    font-size: 0.7rem;
    color: #333;
    flex: 1;
    line-height: 1.5;
    display: flex;
    align-items: center;

    &:empty::before {
      content: attr(placeholder);
      color: #999;
      font-style: italic;
    }
  }

  &__input-clear {
    font-size: 0.9rem;
    color: #999;
    cursor: pointer;
    padding: 0.2rem;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #ff3b30;
    }
  }
}
