import { Button, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import CryptoElevator from '../../components/CryptoElevator/index';
import { CryptoItem } from '../../components/CryptoElevator/types';
import { NavigationBar } from '../../components/Navigation';
import { dataSelectors, useDataStore } from '../../stores/dataStore';
import './index.scss';

// 定义选项类型
interface ModelOption {
  value: string;
  label: string;
}

interface PreferenceOption {
  value: string;
  label: string;
}

interface UserTypeOption {
  value: string;
  label: string;
}

function MarketAnalysis() {
  // 状态管理
  const [selectedCoins, setSelectedCoins] = useState<string[]>([]);
  const [selectedModel, setSelectedModel] = useState('deepseek-chat');
  const [selectedPreference, setSelectedPreference] = useState('');
  const [selectedUserType, setSelectedUserType] = useState('');

  // 搜索相关状态（已被电梯组件替换）
  // const [coinSearchText, setCoinSearchText] = useState('');
  // const [showCoinDropdown, setShowCoinDropdown] = useState(false);

  // 其他下拉框状态
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const [showPreferenceDropdown, setShowPreferenceDropdown] = useState(false);
  const [showUserTypeDropdown, setShowUserTypeDropdown] = useState(false);

  // 电梯组件状态
  const [showCryptoElevator, setShowCryptoElevator] = useState(false);

  // 从dataStore获取状态
  // const availableCoins = useDataStore(dataSelectors.getAvailableCoins());
  // const coinsLoading = useDataStore(dataSelectors.getCoinsLoading());
  // const coinsError = useDataStore(dataSelectors.getCoinsError());
  const analysisResult = useDataStore(dataSelectors.getAnalysisResult());
  const analysisLoading = useDataStore(dataSelectors.getAnalysisLoading());
  const analysisError = useDataStore(dataSelectors.getAnalysisError());
  const selectedCoinForAnalysis = useDataStore(
    dataSelectors.getSelectedCoinForAnalysis()
  );
  const {
    fetchAvailableCoins,
    performAnalysis,
    setSelectedCoinForAnalysis,
    searchCryptos,
  } = useDataStore();

  // 选项数据
  const modelOptions: ModelOption[] = [
    { value: 'deepseek-chat', label: 'DeepSeek Chat' },
  ];

  const preferenceOptions: PreferenceOption[] = [
    { value: 'aggressive', label: '激进' },
    { value: 'conservative', label: '保守' },
    { value: 'neutral', label: '中性' },
  ];

  const userTypeOptions: UserTypeOption[] = [
    { value: 'trader', label: '交易员' },
    { value: 'manager', label: '管理层' },
    { value: 'public', label: '大众用户' },
  ];

  // 获取币种数据
  useEffect(() => {
    fetchAvailableCoins();
  }, [fetchAvailableCoins]);

  // 旧的币种选择逻辑已被电梯组件替换
  // const filteredCoins = availableCoins?.filter(...) || [];
  // const handleCoinSelect = (coinId: string) => { ... };
  // const removeCoin = (coinId: string) => { ... };

  // 分析功能
  const handleAnalyze = () => {
    if (selectedCoins.length === 0) {
      Taro.showToast({
        title: '请选择至少一个币种',
        icon: 'error',
      });
      return;
    }

    if (!selectedPreference) {
      Taro.showToast({
        title: '请选择投资偏好',
        icon: 'error',
      });
      return;
    }

    if (!selectedUserType) {
      Taro.showToast({
        title: '请选择用户类型',
        icon: 'error',
      });
      return;
    }

    // 分析请求
    const analysisRequest = {
      coins: selectedCoins,
      model: selectedModel,
      provider: 'deepseek',
      preference: selectedPreference,
      user_type: selectedUserType,
    };

    performAnalysis(analysisRequest);

    Taro.showToast({
      title: '正在分析中...',
      icon: 'loading',
    });
  };

  // 返回首页
  const handleBack = () => {
    Taro.navigateBack();
  };

  // 关闭所有下拉框
  // const closeAllDropdowns = () => {
  //   setShowCoinDropdown(false);
  //   setShowModelDropdown(false);
  //   setShowPreferenceDropdown(false);
  //   setShowUserTypeDropdown(false);
  // };

  // 处理电梯组件确认选择
  const handleElevatorConfirm = (coins: CryptoItem[]) => {
    if (coins.length > 0) {
      const selectedCoin = coins[0]; // 单选模式，只取第一个
      setSelectedCoinForAnalysis(selectedCoin);
      // 更新本地状态，用于分析
      setSelectedCoins([selectedCoin.id]);
    }
  };

  // 打开电梯组件
  const handleOpenElevator = () => {
    setShowCryptoElevator(true);
    // 初始化搜索数据
    searchCryptos();
  };

  // 渲染模型选择器
  const renderModelSelector = () => {
    return (
      <View className='market-analysis__dropdown-container'>
        <View
          className='market-analysis__selector'
          onClick={() => setShowModelDropdown(!showModelDropdown)}
        >
          <Text className='market-analysis__selector-text'>
            {modelOptions.find(opt => opt.value === selectedModel)?.label ||
              'DeepSeek Chat'}
          </Text>
          <Text className='market-analysis__selector-arrow'>
            {showModelDropdown ? '▲' : '▼'}
          </Text>
        </View>

        {showModelDropdown && (
          <View className='market-analysis__dropdown-list'>
            {modelOptions.map(option => (
              <View
                key={option.value}
                className={`market-analysis__dropdown-item ${
                  selectedModel === option.value
                    ? 'market-analysis__dropdown-item--selected'
                    : ''
                }`}
                onClick={() => {
                  setSelectedModel(option.value);
                  setShowModelDropdown(false);
                }}
              >
                <Text>{option.label}</Text>
                {selectedModel === option.value && (
                  <Text className='market-analysis__coin-selected'>✓</Text>
                )}
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  // 渲染投资偏好选择器
  const renderPreferenceSelector = () => {
    return (
      <View className='market-analysis__dropdown-container'>
        <View
          className='market-analysis__selector'
          onClick={() => setShowPreferenceDropdown(!showPreferenceDropdown)}
        >
          <Text className='market-analysis__selector-text'>
            {selectedPreference
              ? preferenceOptions.find(opt => opt.value === selectedPreference)
                  ?.label
              : '请选择投资偏好'}
          </Text>
          <Text className='market-analysis__selector-arrow'>
            {showPreferenceDropdown ? '▲' : '▼'}
          </Text>
        </View>

        {showPreferenceDropdown && (
          <View className='market-analysis__dropdown-list'>
            {preferenceOptions.map(option => (
              <View
                key={option.value}
                className={`market-analysis__dropdown-item ${
                  selectedPreference === option.value
                    ? 'market-analysis__dropdown-item--selected'
                    : ''
                }`}
                onClick={() => {
                  setSelectedPreference(option.value);
                  setShowPreferenceDropdown(false);
                }}
              >
                <Text>{option.label}</Text>
                {selectedPreference === option.value && (
                  <Text className='market-analysis__coin-selected'>✓</Text>
                )}
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  // 渲染用户类型选择器
  const renderUserTypeSelector = () => {
    return (
      <View className='market-analysis__dropdown-container'>
        <View
          className='market-analysis__selector'
          onClick={() => setShowUserTypeDropdown(!showUserTypeDropdown)}
        >
          <Text className='market-analysis__selector-text'>
            {selectedUserType
              ? userTypeOptions.find(opt => opt.value === selectedUserType)
                  ?.label
              : '请选择用户类型'}
          </Text>
          <Text className='market-analysis__selector-arrow'>
            {showUserTypeDropdown ? '▲' : '▼'}
          </Text>
        </View>

        {showUserTypeDropdown && (
          <View className='market-analysis__dropdown-list'>
            {userTypeOptions.map(option => (
              <View
                key={option.value}
                className={`market-analysis__dropdown-item ${
                  selectedUserType === option.value
                    ? 'market-analysis__dropdown-item--selected'
                    : ''
                }`}
                onClick={() => {
                  setSelectedUserType(option.value);
                  setShowUserTypeDropdown(false);
                }}
              >
                <Text>{option.label}</Text>
                {selectedUserType === option.value && (
                  <Text className='market-analysis__coin-selected'>✓</Text>
                )}
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  // 旧的币种选择器已被电梯组件替换
  // const renderCoinSelector = () => { ... };

  return (
    <View className='market-analysis'>
      {/* 头部导航栏 */}
      <NavigationBar title='市场分析' showBack onBackClick={handleBack} />

      {/* 主要内容区域 */}
      <View className='market-analysis__content'>
        {/* 币种选择器 */}
        <View className='market-analysis__form-item'>
          <Text className='market-analysis__label'>币种选择</Text>

          <View className='market-analysis__input-container'>
            <View
              className='market-analysis__coin-input'
              onClick={handleOpenElevator}
            >
              <Text className='market-analysis__coin-input-text'>
                {selectedCoinForAnalysis
                  ? `${selectedCoinForAnalysis.name} (${selectedCoinForAnalysis.symbol})`
                  : '点击选择币种'}
              </Text>
            </View>
            {selectedCoinForAnalysis && (
              <Text
                className='market-analysis__input-clear'
                onClick={() => {
                  setSelectedCoinForAnalysis(null);
                  setSelectedCoins([]);
                }}
              >
                ×
              </Text>
            )}
          </View>
        </View>

        {/* 模型选择器 */}
        <View className='market-analysis__form-item'>
          <Text className='market-analysis__label'>AI模型</Text>
          {renderModelSelector()}
        </View>

        {/* 投资偏好选择器 */}
        <View className='market-analysis__form-item'>
          <Text className='market-analysis__label'>投资偏好</Text>
          {renderPreferenceSelector()}
        </View>

        {/* 用户类型选择器 */}
        <View className='market-analysis__form-item'>
          <Text className='market-analysis__label'>用户类型</Text>
          {renderUserTypeSelector()}
        </View>

        {/* 分析结果展示 */}
        {analysisResult && (
          <View className='market-analysis__result'>
            <Text className='market-analysis__result-title'>分析结果</Text>
            <View className='market-analysis__result-content'>
              {JSON.stringify(analysisResult, null, 2)}
            </View>
          </View>
        )}

        {analysisError && (
          <View className='market-analysis__error'>
            <Text>分析失败: {analysisError}</Text>
          </View>
        )}
      </View>

      {/* 分析按钮 */}
      <View className='market-analysis__analyze-section'>
        <Button
          className='market-analysis__analyze-btn'
          onClick={handleAnalyze}
          disabled={
            selectedCoins.length === 0 ||
            !selectedPreference ||
            !selectedUserType ||
            analysisLoading
          }
        >
          {analysisLoading ? '分析中...' : '开始分析'}
        </Button>
      </View>

      {/* 电梯组件 */}
      <CryptoElevator
        visible={showCryptoElevator}
        onClose={() => setShowCryptoElevator(false)}
        onConfirm={handleElevatorConfirm}
        mode='single'
        title='市场分析'
        preSelectedCoins={
          selectedCoinForAnalysis ? [selectedCoinForAnalysis] : []
        }
      />
    </View>
  );
}

export default MarketAnalysis;
