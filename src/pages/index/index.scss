.index-page {
  min-height: 100vh;
  background: var(--color-background);
  color: var(--color-text-primary);
  padding-bottom: 5rem;
}

// 认证演示区域
.auth-demo-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--color-surface);
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  // 暗色主题适配
  [data-theme='dark'] & {
    background: #2d3748;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.header-section {
  padding: 1rem 0;
  margin-bottom: 1.5rem;

  .theme-toggle {
    display: flex;
    justify-content: flex-end;
  }
}

.section-header {
  padding: 0 0.6rem;
}

.feature-cards {
  margin: 1rem 2rem;

  .feature-card {
    padding: 0.5rem;
    background: var(--color-surface);
    border-radius: 12px;
    border: 1px solid var(--color-border);
    transition: all 0.2s ease-in-out;
    margin: 0.3rem 0;

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .feature-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
      display: block;
    }

    .feature-desc {
      font-size: 0.875rem;
      color: var(--color-text-secondary);
      line-height: 1.5;
      display: block;
    }
  }
}

.section-title {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-primary);
  display: inline-block;
}

.trending-crypto {
  margin-bottom: 2rem;

  .crypto-card {
    padding: 1rem;
    background: var(--color-surface);
    border-radius: 8px;
    border: 1px solid var(--color-border);
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }

    .crypto-header {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;

      .crypto-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 0.5rem;
      }

      .crypto-info {
        .crypto-symbol {
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
          display: block;
        }

        .crypto-name {
          font-size: 0.75rem;
          color: var(--color-text-secondary);
          display: block;
        }
      }
    }

    .crypto-price {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-primary);
      margin-bottom: 0.25rem;
      display: block;
    }

    .crypto-change {
      font-size: 0.875rem;
      font-weight: 500;
      display: block;

      &.positive {
        color: var(--color-success);
      }

      &.negative {
        color: var(--color-error);
      }
    }
  }
}

.trending-news {
  margin-bottom: 2rem;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .section-title {
      color: var(--color-text-primary);
    }
  }

  // 首页NewsComponent样式定制 - 适配首页预览模式
  .index-news-component {
    height: 100%;
    border-radius: 8px;
    border: 1px solid var(--color-border);
    // overflow: hidden;
    background: var(--color-surface);

    // 隐藏粘性布局，因为首页不需要
    .news-categories {
      display: block;
    }

    // 调整新闻列表容器
    .news-list-container {
      height: auto !important;
    }

    // 调整新闻列表样式
    .news-list {
      padding: 0;

      .news-item {
        border: none;
        border-bottom: 1px solid var(--color-border);
        border-radius: 0;
        margin-bottom: 0;
        background: transparent;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: var(--color-surface-hover, rgba(0, 0, 0, 0.02));
          transform: none;
          box-shadow: none;
        }
      }
    }

    // 查看更多按钮样式
    .view-more {
      background: var(--color-background);
      border-top: 1px solid var(--color-border);
      margin: 0;
      padding: 1rem;
    }
  }

  // 保留原有的news-card样式作为备用
  .news-card {
    padding: 1rem;
    background: var(--color-surface);
    border-radius: 8px;
    border: 1px solid var(--color-border);
    margin-bottom: 1rem;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }

    .news-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
      display: block;
      line-height: 1.4;
    }

    .news-summary {
      font-size: 0.875rem;
      color: var(--color-text-secondary);
      margin-bottom: 0.5rem;
      display: block;
      line-height: 1.5;
    }

    .news-meta {
      font-size: 0.75rem;
      color: var(--color-text-secondary);
      display: block;
    }

    .news-image {
      width: 100%;
      border-radius: 4px;
      // overflow: hidden;
    }
  }
}

.platform-tips {
  padding: 1rem;
  background: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--color-border);
  text-align: center;
  margin-top: 2rem;

  .tips-text {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    display: block;
  }
}
