import { ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useEffect, useState } from 'react';
import Carousel from '../../components/Carousel';
import Container from '../../components/Layout/Container';
import Grid from '../../components/Layout/Grid';
import ResponsiveText from '../../components/Layout/ResponsiveText';
import LazyImage from '../../components/LazyLoad/LazyImage';
import { InlineLoading } from '../../components/LoadingIndicator';
import { NavigationBar } from '../../components/Navigation';
import NewsComponent from '../../components/News/NewsComponent';
import { CryptoCardSkeleton } from '../../components/Skeleton';
import TabBar from '../../components/TabBar';
import {
  configSelectors,
  cryptoSelectors,
  useConfigStore,
  useCryptoStore,
  useUserStore,
} from '../../stores';
import { loadingSelectors, useLoadingStore } from '../../stores/loadingStore';
import { navigation } from '../../utils/platform';
import './index.scss';

const Index: React.FC = () => {
  // 使用新的store结构
  const { isAuthenticated } = useUserStore();

  // 配置相关状态
  const baseConfig = useConfigStore(configSelectors.getBaseConfig());
  const configLoading = useConfigStore(configSelectors.getConfigLoading());
  const { fetchBaseConfig } = useConfigStore();

  // 加密货币相关状态
  const cryptocurrencies = useCryptoStore(
    cryptoSelectors.getCryptocurrencies()
  );
  const { fetchCryptocurrencies } = useCryptoStore();

  // 使用加载状态管理
  const cryptoLoading = useLoadingStore(
    loadingSelectors.getLoadingById('crypto')
  );

  const [refreshing, setRefreshing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // 检查登录状态并提示
  const checkAuthAndPrompt = () => {
    if (!isAuthenticated) {
      Taro.showModal({
        title: '提示',
        content: '请先登录后再使用此功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: res => {
          if (res.confirm) {
            navigation.navigateTo('/pages/auth/index');
          }
        },
      });
      return false;
    }
    return true;
  };

  useEffect(() => {
    // 渐进式数据加载 - 避免 Promise.all 导致的白屏
    const initializeData = async () => {
      try {
        setIsInitialized(false);

        // 1. 优先确保基本配置已加载（如果还没有的话）
        if (!baseConfig) {
          try {
            await fetchBaseConfig();
          } catch (error) {
            console.warn('基本配置加载失败，继续加载其他数据:', error);
          }
        }

        // 2. 延迟加载加密货币数据（非阻塞）
        setTimeout(() => {
          fetchCryptocurrencies().catch(error => {
            console.error('加密货币数据加载失败:', error);
          });
        }, 100);

        setIsInitialized(true);
      } catch (error) {
        console.error('数据初始化失败:', error);
        setIsInitialized(true);
      }
    };

    initializeData();
  }, [fetchCryptocurrencies, fetchBaseConfig, baseConfig]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // 刷新时可以并行加载，因为用户主动触发
      await Promise.allSettled([fetchBaseConfig(), fetchCryptocurrencies()]);
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <>
      {/* 全局加载指示器 */}
      {/* <GlobalLoadingOverlay showProgress={false} showMessage backdrop /> */}
      <NavigationBar
        title='加密货币资讯'
        showBack={false}
        showThemeToggle={true}
      />

      {/* 显示初始化状态 */}
      {!isInitialized && (
        <Container maxWidth='xl' padding>
          <View className='initialization-loading'>
            <InlineLoading
              loadingId='initialization'
              text='正在初始化应用...'
              size='small'
            />
          </View>
        </Container>
      )}
      <ScrollView
        className='index-page'
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        <Container maxWidth='xl' padding>
          {/* 轮播组件 */}
          <Carousel height='200px' />

          {/* 热门加密货币 */}
          <View className='trending-crypto'>
            <View className='section-header'>
              <ResponsiveText
                variant='h5'
                weight='semibold'
                className='section-title'
              >
                热门加密货币
              </ResponsiveText>
              {cryptoLoading && (
                <InlineLoading
                  loadingId='crypto'
                  text='加载中...'
                  size='small'
                />
              )}
            </View>

            {cryptoLoading ? (
              <CryptoCardSkeleton count={3} />
            ) : (
              <Grid container spacing={4} justify={'center'}>
                {cryptocurrencies.map(crypto => {
                  return (
                    <Grid item xs={12} sm={6} md={4} key={crypto.id}>
                      <View className='crypto-card'>
                        <View className='crypto-header'>
                          <LazyImage
                            src={crypto.imageUrl || ''}
                            alt={crypto.name}
                            width={32}
                            height={32}
                            className='crypto-icon'
                          />
                          <View className='crypto-info'>
                            <Text className='crypto-symbol'>
                              {crypto.symbol.toUpperCase()}
                            </Text>
                            <Text className='crypto-name'>{crypto.name}</Text>
                          </View>
                        </View>
                      </View>
                    </Grid>
                  );
                })}
              </Grid>
            )}
          </View>

          {/* 热门资讯 - 使用NewsComponent */}
          {/* 使用NewsComponent组件，配置为首页预览模式 */}
          <NewsComponent
            showTabs={true} // 隐藏分类tabs
            enableRefresh={true} // 禁用下拉刷新
            enableInfiniteScroll={true} // 禁用无限滚动
            containerHeight='100vh'
            className='index-news-component'
          />
        </Container>
      </ScrollView>

      {/* 底部导航 */}
      <TabBar current={0} />
    </>
  );
};

export default Index;
