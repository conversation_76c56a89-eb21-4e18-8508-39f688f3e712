import { ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useEffect, useState } from 'react';
import Carousel from '../../components/Carousel';
import Container from '../../components/Layout/Container';
import Grid from '../../components/Layout/Grid';
import ResponsiveText from '../../components/Layout/ResponsiveText';
import LazyImage from '../../components/LazyLoad/LazyImage';
import { InlineLoading } from '../../components/LoadingIndicator';
import { NavigationBar } from '../../components/Navigation';
import NewsComponent from '../../components/News/NewsComponent';
import { CryptoCardSkeleton } from '../../components/Skeleton';
import TabBar from '../../components/TabBar';
import { useUserStore } from '../../stores';
import { dataSelectors, useDataStore } from '../../stores/dataStore';
import { loadingSelectors, useLoadingStore } from '../../stores/loadingStore';
import { navigation } from '../../utils/platform';
import './index.scss';

const Index: React.FC = () => {
  // 使用选择器优化订阅
  const cryptocurrencies = useDataStore(dataSelectors.getCryptocurrencies());
  const { fetchCryptocurrencies } = useDataStore();
  const { isAuthenticated } = useUserStore();

  // 使用加载状态管理
  const cryptoLoading = useLoadingStore(
    loadingSelectors.getLoadingById('crypto')
  );

  const [refreshing, setRefreshing] = useState(false);

  // 检查登录状态并提示
  const checkAuthAndPrompt = () => {
    if (!isAuthenticated) {
      Taro.showModal({
        title: '提示',
        content: '请先登录后再使用此功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: res => {
          if (res.confirm) {
            navigation.navigateTo('/pages/auth/index');
          }
        },
      });
      return false;
    }
    return true;
  };

  useEffect(() => {
    // 初始化数据加载
    const initializeData = async () => {
      try {
        await Promise.all([fetchCryptocurrencies()]);
      } catch (error) {
        console.error('数据初始化失败:', error);
      }
    };
    initializeData();
  }, [fetchCryptocurrencies]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([fetchCryptocurrencies()]);
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <>
      {/* 全局加载指示器 */}
      {/* <GlobalLoadingOverlay showProgress={false} showMessage backdrop /> */}
      <NavigationBar
        title='加密货币资讯'
        showBack={false}
        showThemeToggle={true}
      />
      <ScrollView
        className='index-page'
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        <Container maxWidth='xl' padding>
          {/* 轮播组件 */}
          <Carousel height='200px' />

          {/* 热门加密货币 */}
          <View className='trending-crypto'>
            <View className='section-header'>
              <ResponsiveText
                variant='h5'
                weight='semibold'
                className='section-title'
              >
                热门加密货币
              </ResponsiveText>
              {cryptoLoading && (
                <InlineLoading
                  loadingId='crypto'
                  text='加载中...'
                  size='small'
                />
              )}
            </View>

            {cryptoLoading ? (
              <CryptoCardSkeleton count={3} />
            ) : (
              <Grid container spacing={4} justify={'center'}>
                {cryptocurrencies.map(crypto => {
                  return (
                    <Grid item xs={12} sm={6} md={4} key={crypto.id}>
                      <View className='crypto-card'>
                        <View className='crypto-header'>
                          <LazyImage
                            src={crypto.imageUrl || ''}
                            alt={crypto.name}
                            width={32}
                            height={32}
                            className='crypto-icon'
                          />
                          <View className='crypto-info'>
                            <Text className='crypto-symbol'>
                              {crypto.symbol.toUpperCase()}
                            </Text>
                            <Text className='crypto-name'>{crypto.name}</Text>
                          </View>
                        </View>
                      </View>
                    </Grid>
                  );
                })}
              </Grid>
            )}
          </View>

          {/* 热门资讯 - 使用NewsComponent */}
          {/* 使用NewsComponent组件，配置为首页预览模式 */}
          <NewsComponent
            showTabs={true} // 隐藏分类tabs
            enableRefresh={true} // 禁用下拉刷新
            enableInfiniteScroll={true} // 禁用无限滚动
            containerHeight='100vh'
            className='index-news-component'
          />
        </Container>
      </ScrollView>

      {/* 底部导航 */}
      <TabBar current={0} />
    </>
  );
};

export default Index;
