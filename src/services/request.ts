import Taro from '@tarojs/taro';
import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosRequestHeaders,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean;
  showError?: boolean;
  loadingText?: string;
}

// 响应数据接口
export interface ResponseData<T = unknown> {
  code: number;
  data: T;
  message: string;
  success: boolean;
}

// 创建axios实例
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL:
      process.env.NODE_ENV === 'development'
        ? '/api'
        : 'https://api.example.com',
    timeout: 5 * 60 * 1000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig & RequestConfig) => {
      // 显示加载提示
      if (config.showLoading !== false) {
        Taro.showLoading({
          title: config.loadingText || '加载中...',
          mask: true,
        });
      }

      // 严格类型设置 headers
      const headers = (config.headers || {}) as AxiosRequestHeaders;
      const token = Taro.getStorageSync('token');
      if (token) headers.Authorization = `Bearer ${token}`;
      headers['X-Platform'] = process.env.TARO_ENV || 'h5';
      config.headers = headers;

      return config;
    },
    (error: AxiosError) => {
      Taro.hideLoading();
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      Taro.hideLoading();

      const { data } = response;
      console.log('API Response:', data);
      // 请求成功
      if (data.success || data.code === 200) {
        return data.data;
      }

      // 业务错误处理
      const errorMessage = data.message || '请求失败';

      if ((response.config as RequestConfig).showError !== false) {
        Taro.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000,
        });
      }

      return Promise.reject(new Error(errorMessage));
    },
    (error: AxiosError) => {
      Taro.hideLoading();

      let errorMessage = '网络错误，请稍后重试';

      if (error.response) {
        const { status, data } = error.response;

        switch (status) {
          case 401:
            errorMessage = '登录已过期，请重新登录';
            // 清除token并跳转到登录页
            Taro.removeStorageSync('token');
            Taro.reLaunch({
              url: '/pages/login/index',
            });
            break;
          case 403:
            errorMessage = '没有权限访问';
            break;
          case 404:
            errorMessage = '请求的资源不存在';
            break;
          case 500:
            errorMessage = '服务器内部错误';
            break;
          default: {
            const msg =
              data && typeof data === 'object' && 'message' in data
                ? (data as { message?: string }).message
                : undefined;
            errorMessage =
              typeof msg === 'string' && msg ? msg : `请求失败 (${status})`;
          }
        }
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请检查网络连接';
      } else if (
        error.message?.includes('port closed') ||
        error.message?.includes('AggregateError') ||
        error.code === 'ECONNREFUSED'
      ) {
        // 代理错误或连接错误，静默处理
        console.warn('API连接失败:', error.message);
        return Promise.resolve({
          data: null,
          error: 'API服务暂时不可用',
          success: false,
        });
      } else if (!error.response) {
        errorMessage = '网络连接失败，请检查网络设置';
      }

      if ((error.config as RequestConfig)?.showError !== false) {
        Taro.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000,
        });
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// 创建请求实例
const request = createAxiosInstance();

// 封装请求方法
export const http = {
  get: <T = unknown>(
    url: string,
    config?: RequestConfig
  ): Promise<ResponseData<T>> => {
    return request.get(url, config);
  },

  post: <T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ResponseData<T>> => {
    return request.post(url, data, config);
  },

  put: <T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ResponseData<T>> => {
    return request.put(url, data, config);
  },

  delete: <T = unknown>(
    url: string,
    config?: RequestConfig
  ): Promise<ResponseData<T>> => {
    return request.delete(url, config);
  },

  patch: <T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ResponseData<T>> => {
    return request.patch(url, data, config);
  },
};

export default request;
