import { BaseRequest, NewsItemType, User } from '../types';
import type { AnalysisResponseType } from '../types/analysisResponseType';
import type { NewsListParams, NewsListResponse } from '../types/newsType';
import { mockApiService } from './mockData';
import { http, ResponseData } from './request';

// 币种配置接口（API专用，不包含价格等动态数据）
export interface CoinConfig {
  id: string;
  name: string;
  symbol: string;
}

// 币种分析结果类型（API返回格式）
export interface CoinAnalysisResult {
  coin_id: string;
  coin_name: string;
  coin_symbol: string;
  success: boolean;
  analysis_time: string;
  current_price?: number;
  price_change_24h?: number;
  news_count: number;
  news_list: NewsItemType[];
  url_list: string[];
  ai_analysis: string;
  overall_sentiment?: string;
  overall_sentiment_score?: number;
  short_term_trend?: string;
  mid_term_trend?: string;
  strategy?: string;
  risk_warning?: string;
  summary_of_news?: string;
  links_to_reference_news_articles?: string[];
  news_analysis?: Record<string, any>;
  error_message?: string;
}

// 分析请求参数
export interface AnalysisRequest {
  coins: string[];
  model?: string;
  provider?: string;
  news_days?: number;
  preference?: string;
  user_type?: string;
  tone?: string;
  audience?: string;
}

// 分析响应类型
export interface AnalysisResponse extends AnalysisResponseType {}

// 新闻列表API响应类型

// 新闻详情API响应类型
export interface NewsDetailResponse {
  id: string;
  title: string;
  description: string;
  content: string;
  url: string;
  source: string;
  published_at: string;
  fetched_at: string;
  is_mock: boolean;
}

// 币种列表API响应类型
export interface CoinListResponse {
  success: boolean;
  data: CoinConfig[];
  total: number;
}

// 健康检查响应类型
export interface HealthResponse {
  status: string;
  timestamp: string;
  service: string;
}

// 模型配置响应类型
export interface ModelsResponse {
  success: boolean;
  models: Record<string, any>;
}

// 用户登录/注册响应类型
export interface AuthResponse {
  token: string;
  user: User;
}

// 用户资料响应类型
export interface UserProfileResponse extends User {}

// 加密货币搜索项类型
export interface CryptoSearchItem {
  id: string;
  symbol: string;
  name: string;
  image: string;
}

// 加密货币搜索响应类型
export interface CryptoSearchResponse {
  key: string;
  total: number;
  results: CryptoSearchItem[];
}

// API服务
const api = {
  // 健康检查
  health: {
    check: (): Promise<ResponseData<HealthResponse>> => {
      return http.get<HealthResponse>('/v1/health');
    },
  },

  // 加密货币相关接口
  crypto: {
    // 获取加密货币列表（对应后端的 /api/v1/config/coins）
    getList: async (params?: {
      page?: number;
      sort?: string;
    }): Promise<ResponseData<CoinListResponse>> => {
      try {
        return await http.get<CoinListResponse>('/v1/config/coins', { params });
      } catch (error) {
        console.warn('使用模拟数据作为后备:', error);
        const mockResult = await mockApiService.getCoins(params?.page);
        return {
          data: mockResult.data as any,
          success: true,
          message: '数据来源：模拟数据',
          code: 200,
        } as ResponseData<CoinListResponse>;
      }
    },

    // 获取可用模型列表
    getModels: (): Promise<ResponseData<ModelsResponse>> => {
      return http.get<ModelsResponse>('/v1/config/models');
    },

    // 币种分析接口（分析耗时较长，单独提升超时时间）
    analyze: (
      data: AnalysisRequest
    ): Promise<ResponseData<AnalysisResponse>> => {
      return http.post<AnalysisResponse>('/v1/analyze', data, {
        timeout: 60 * 1000 * 3, // 3分钟
        loadingText: '智能分析中...（约1-3分钟）',
      });
    },
    // 获取指定加密货币分析结果
    getAnalysisTrend: (
      data: BaseRequest
    ): Promise<ResponseData<AnalysisResponse>> => {
      return http.post<AnalysisResponse>(`/v1/analyze`, data);
    },

    // 搜索加密货币
    search: async (params?: {
      key?: string;
    }): Promise<ResponseData<CryptoSearchResponse>> => {
      try {
        const result = await http.get<CryptoSearchResponse>(
          '/v1/crypto/search',
          {
            params,
          }
        );
        return result;
      } catch (error) {
        console.warn('API 请求失败，使用模拟搜索数据作为后备:', error);
        const mockResult = await mockApiService.searchCoins(params?.key);
        const response = {
          data: mockResult.data as any,
          success: true,
          message: '数据来源：模拟数据',
          code: 200,
        } as ResponseData<CryptoSearchResponse>;
        return response;
      }
    },
  },

  // 新闻相关接口
  news: {
    // 获取新闻列表 - 使用新API格式
    getList: async (
      params?: NewsListParams
    ): Promise<ResponseData<NewsListResponse>> => {
      try {
        return await http.get<NewsListResponse>('/v1/news', { params });
      } catch (error) {
        console.warn('使用模拟新闻数据作为后备:', error);
        const mockResult = await mockApiService.getNews(
          params?.page_size,
          params?.page
        );
        return {
          data: mockResult.data as any,
          success: true,
          message: '数据来源：模拟数据',
          code: 200,
        } as ResponseData<NewsListResponse>;
      }
    },

    // 获取新闻详情 - 使用新API格式
    getDetail: (url: string): Promise<ResponseData<NewsDetailResponse>> => {
      const encodedUrl = encodeURIComponent(url);
      return http.get<NewsDetailResponse>(`/v1/news/detail?url=${encodedUrl}`);
    },
  },

  // 用户相关接口
  user: {
    // 登录
    login: (data: {
      username: string;
      password: string;
    }): Promise<ResponseData<AuthResponse>> => {
      return http.post<AuthResponse>('/user/login', data);
    },

    // 注册
    register: (data: {
      username: string;
      password: string;
      email: string;
    }): Promise<ResponseData<AuthResponse>> => {
      return http.post<AuthResponse>('/user/register', data);
    },

    // 微信登录
    wechatLogin: (data: {
      code: string;
      userInfo?: any;
    }): Promise<ResponseData<AuthResponse>> => {
      return http.post<AuthResponse>('/user/wechat-login', data);
    },

    // 发送验证码
    sendVerificationCode: (data: {
      account: string;
      type: 'email' | 'phone';
    }): Promise<ResponseData<{ success: boolean }>> => {
      return http.post<{ success: boolean }>('/user/send-code', data);
    },

    // 验证码登录
    loginWithCode: (data: {
      account: string;
      code: string;
      type: 'email' | 'phone';
    }): Promise<ResponseData<AuthResponse>> => {
      return http.post<AuthResponse>('/user/login-with-code', data);
    },

    // 重置密码
    resetPassword: (data: {
      account: string;
      code: string;
      newPassword: string;
      type: 'email' | 'phone';
    }): Promise<ResponseData<{ success: boolean }>> => {
      return http.post<{ success: boolean }>('/user/reset-password', data);
    },

    // 获取用户信息
    getProfile: (): Promise<ResponseData<UserProfileResponse>> => {
      return http.get<UserProfileResponse>('/user/profile');
    },

    // 更新用户信息
    updateProfile: (
      data: Partial<User>
    ): Promise<ResponseData<UserProfileResponse>> => {
      return http.put<UserProfileResponse>('/user/profile', data);
    },
  },
};

export default api;
