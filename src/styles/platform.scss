// 平台特定样式

// 微信小程序样式
.platform-weapp {
  // 小程序特有的样式调整
  .scroll-view {
    height: 100vh;
  }

  .navigation-bar {
    height: 44px;
    background: var(--color-background);
    border-bottom: 1px solid var(--color-border);
  }

  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  // 小程序按钮样式重置
  button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font-size: inherit;
    color: inherit;

    &::after {
      border: none;
    }
  }

  // 小程序输入框样式
  input {
    background: transparent;
    border: none;
    outline: none;
  }

  // 小程序图片样式
  image {
    display: block;
    max-width: 100%;
    height: auto;
  }
}

// H5样式
.platform-h5 {
  // H5特有的样式调整
  .container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .navigation-bar {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: var(--color-background);
    border-bottom: 1px solid var(--color-border);
    backdrop-filter: blur(10px);
  }

  // H5响应式布局
  @media (min-width: 768px) {
    .mobile-only {
      display: none !important;
    }

    .desktop-grid {
      display: grid;
      grid-template-columns: 250px 1fr;
      gap: 2rem;
    }

    .sidebar {
      position: sticky;
      top: 80px;
      height: calc(100vh - 80px);
      overflow-y: auto;
    }
  }

  @media (max-width: 767px) {
    .desktop-only {
      display: none !important;
    }

    .mobile-grid {
      display: block;
    }
  }

  // H5滚动条样式
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--color-surface);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: 4px;

    &:hover {
      background: var(--color-text-secondary);
    }
  }

  // H5鼠标悬停效果
  .hover-effect {
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

// 支付宝小程序样式
.platform-alipay {
  // 支付宝小程序特有样式
  .navigation-bar {
    background: #1677ff;
    color: white;
  }

  .primary-color {
    color: #1677ff;
  }

  .primary-bg {
    background-color: #1677ff;
  }
}

// 百度小程序样式
.platform-swan {
  // 百度小程序特有样式
  .navigation-bar {
    background: #3385ff;
    color: white;
  }

  .primary-color {
    color: #3385ff;
  }

  .primary-bg {
    background-color: #3385ff;
  }
}

// 字节跳动小程序样式
.platform-tt {
  // 字节跳动小程序特有样式
  .navigation-bar {
    background: #fe2c55;
    color: white;
  }

  .primary-color {
    color: #fe2c55;
  }

  .primary-bg {
    background-color: #fe2c55;
  }
}

// QQ小程序样式
.platform-qq {
  // QQ小程序特有样式
  .navigation-bar {
    background: #12b7f5;
    color: white;
  }

  .primary-color {
    color: #12b7f5;
  }

  .primary-bg {
    background-color: #12b7f5;
  }
}

// 京东小程序样式
.platform-jd {
  // 京东小程序特有样式
  .navigation-bar {
    background: #e93323;
    color: white;
  }

  .primary-color {
    color: #e93323;
  }

  .primary-bg {
    background-color: #e93323;
  }
}

// React Native样式
.platform-rn {
  // RN特有样式
  .container {
    flex: 1;
  }

  .scroll-view {
    flex: 1;
  }

  .text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }
}

// 通用平台适配工具类
.platform-adaptive {
  // 小程序端隐藏
  &.hide-miniprogram {
    .platform-weapp &,
    .platform-alipay &,
    .platform-swan &,
    .platform-tt &,
    .platform-qq &,
    .platform-jd & {
      display: none !important;
    }
  }

  // H5端隐藏
  &.hide-h5 {
    .platform-h5 & {
      display: none !important;
    }
  }

  // RN端隐藏
  &.hide-rn {
    .platform-rn & {
      display: none !important;
    }
  }

  // 仅小程序端显示
  &.show-miniprogram-only {
    display: none !important;

    .platform-weapp &,
    .platform-alipay &,
    .platform-swan &,
    .platform-tt &,
    .platform-qq &,
    .platform-jd & {
      display: block !important;
    }
  }

  // 仅H5端显示
  &.show-h5-only {
    display: none !important;

    .platform-h5 & {
      display: block !important;
    }
  }

  // 仅RN端显示
  &.show-rn-only {
    display: none !important;

    .platform-rn & {
      display: block !important;
    }
  }
}

// 响应式字体大小
.responsive-text {
  // 小程序端
  .platform-weapp &,
  .platform-alipay &,
  .platform-swan &,
  .platform-tt &,
  .platform-qq &,
  .platform-jd & {
    font-size: 28rpx;
    line-height: 1.5;
  }

  // H5端
  .platform-h5 & {
    font-size: 14px;
    line-height: 1.5;

    @media (min-width: 768px) {
      font-size: 16px;
    }

    @media (min-width: 1024px) {
      font-size: 18px;
    }
  }

  // RN端
  .platform-rn & {
    font-size: 16;
    line-height: 24;
  }
}

// 响应式间距
.responsive-padding {
  // 小程序端
  .platform-weapp &,
  .platform-alipay &,
  .platform-swan &,
  .platform-tt &,
  .platform-qq &,
  .platform-jd & {
    padding: 24rpx;
  }

  // H5端
  .platform-h5 & {
    padding: 12px;

    @media (min-width: 768px) {
      padding: 16px;
    }

    @media (min-width: 1024px) {
      padding: 24px;
    }
  }

  // RN端
  .platform-rn & {
    padding: 16;
  }
}
