import Taro from '@tarojs/taro';
import type { Platform } from '../types';

// 获取当前平台
export const getCurrentPlatform = (): Platform => {
  return (process.env.TARO_ENV as Platform) || 'h5';
};

// 平台检测函数
export const platform = {
  // 是否为微信小程序
  isWeapp: () => getCurrentPlatform() === 'weapp',

  // 是否为支付宝小程序
  isAlipay: () => getCurrentPlatform() === 'alipay',

  // 是否为百度小程序
  isSwan: () => getCurrentPlatform() === 'swan',

  // 是否为字节跳动小程序
  isTt: () => getCurrentPlatform() === 'tt',

  // 是否为QQ小程序
  isQq: () => getCurrentPlatform() === 'qq',

  // 是否为京东小程序
  isJd: () => getCurrentPlatform() === 'jd',

  // 是否为H5
  isH5: () => getCurrentPlatform() === 'h5',

  // 是否为React Native
  isRn: () => getCurrentPlatform() === 'rn',

  // 是否为小程序环境
  isMiniProgram: () => {
    const env = getCurrentPlatform();
    return ['weapp', 'alipay', 'swan', 'tt', 'qq', 'jd'].includes(env);
  },

  // 是否为Web环境
  isWeb: () => {
    const env = getCurrentPlatform();
    return ['h5'].includes(env);
  },

  // 是否为移动端
  isMobile: () => {
    const env = getCurrentPlatform();
    return ['weapp', 'alipay', 'swan', 'tt', 'qq', 'jd', 'h5', 'rn'].includes(
      env
    );
  },
};

// 获取平台特定的样式类名
export const getPlatformClassName = (baseClass: string): string => {
  const currentPlatform = getCurrentPlatform();
  return `${baseClass} ${baseClass}--${currentPlatform}`;
};

// 获取平台特定的配置
export const getPlatformConfig = () => {
  const currentPlatform = getCurrentPlatform();

  const configs = {
    weapp: {
      navigationBarTitleText: '加密货币资讯',
      navigationBarBackgroundColor: '#ffffff',
      navigationBarTextStyle: 'black',
      backgroundColor: '#f8f8f8',
      enablePullDownRefresh: true,
      onReachBottomDistance: 50,
    },
    h5: {
      navigationBarTitleText: '加密货币资讯',
      navigationBarBackgroundColor: '#ffffff',
      navigationBarTextStyle: 'black',
      backgroundColor: '#f8f8f8',
    },
    alipay: {
      navigationBarTitleText: '加密货币资讯',
      navigationBarBackgroundColor: '#ffffff',
      navigationBarTextStyle: 'black',
      backgroundColor: '#f8f8f8',
      enablePullDownRefresh: true,
    },
    swan: {
      navigationBarTitleText: '加密货币资讯',
      navigationBarBackgroundColor: '#ffffff',
      navigationBarTextStyle: 'black',
      backgroundColor: '#f8f8f8',
      enablePullDownRefresh: true,
    },
    tt: {
      navigationBarTitleText: '加密货币资讯',
      navigationBarBackgroundColor: '#ffffff',
      navigationBarTextStyle: 'black',
      backgroundColor: '#f8f8f8',
      enablePullDownRefresh: true,
    },
    qq: {
      navigationBarTitleText: '加密货币资讯',
      navigationBarBackgroundColor: '#ffffff',
      navigationBarTextStyle: 'black',
      backgroundColor: '#f8f8f8',
      enablePullDownRefresh: true,
    },
    jd: {
      navigationBarTitleText: '加密货币资讯',
      navigationBarBackgroundColor: '#ffffff',
      navigationBarTextStyle: 'black',
      backgroundColor: '#f8f8f8',
      enablePullDownRefresh: true,
    },
    rn: {
      navigationBarTitleText: '加密货币资讯',
      navigationBarBackgroundColor: '#ffffff',
      navigationBarTextStyle: 'black',
      backgroundColor: '#f8f8f8',
    },
  };

  return configs[currentPlatform] || configs.h5;
};

// 获取平台特定的API能力
export const getPlatformCapabilities = () => {
  const currentPlatform = getCurrentPlatform();

  return {
    // 是否支持下拉刷新
    supportsPullToRefresh: platform.isMiniProgram(),

    // 是否支持分享
    supportsShare: platform.isMiniProgram(),

    // 是否支持扫码
    supportsScanCode: platform.isMiniProgram(),

    // 是否支持支付
    supportsPayment: platform.isWeapp() || platform.isAlipay(),

    // 是否支持定位
    supportsLocation: platform.isMiniProgram() || platform.isH5(),

    // 是否支持相机
    supportsCamera: platform.isMiniProgram(),

    // 是否支持录音
    supportsRecording: platform.isMiniProgram(),

    // 是否支持蓝牙
    supportsBluetooth: platform.isMiniProgram(),

    // 是否支持NFC
    supportsNFC: platform.isWeapp(),

    // 是否支持生物认证
    supportsBiometric: platform.isWeapp() || platform.isAlipay(),

    // 是否支持剪贴板
    supportsClipboard: true,

    // 是否支持本地存储
    supportsStorage: true,

    // 是否支持网络状态检测
    supportsNetworkStatus: true,

    // 是否支持设备信息获取
    supportsDeviceInfo: true,

    // 是否支持震动
    supportsVibration: platform.isMiniProgram() || platform.isH5(),
  };
};

// 平台特定的导航方法
export const navigation = {
  // 跳转到页面
  navigateTo: (url: string, params?: Record<string, any>) => {
    const queryString = params
      ? '?' +
        Object.entries(params)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&')
      : '';

    return Taro.navigateTo({
      url: url + queryString,
    });
  },

  // 重定向到页面
  redirectTo: (url: string, params?: Record<string, any>) => {
    const queryString = params
      ? '?' +
        Object.entries(params)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&')
      : '';

    return Taro.redirectTo({
      url: url + queryString,
    });
  },

  // 返回上一页
  navigateBack: (delta = 1) => {
    return Taro.navigateBack({ delta });
  },

  // 重新启动到页面
  reLaunch: (url: string, params?: Record<string, any>) => {
    const queryString = params
      ? '?' +
        Object.entries(params)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&')
      : '';

    return Taro.reLaunch({
      url: url + queryString,
    });
  },

  // 切换到tab页面
  switchTab: (url: string) => {
    return Taro.switchTab({ url });
  },
};

// 平台特定的存储方法
export const storage = {
  // 同步设置存储
  setSync: (key: string, data: any) => {
    return Taro.setStorageSync(key, data);
  },

  // 同步获取存储
  getSync: (key: string) => {
    return Taro.getStorageSync(key);
  },

  // 同步移除存储
  removeSync: (key: string) => {
    return Taro.removeStorageSync(key);
  },

  // 同步清空存储
  clearSync: () => {
    return Taro.clearStorageSync();
  },

  // 异步设置存储
  set: (key: string, data: any) => {
    return Taro.setStorage({ key, data });
  },

  // 异步获取存储
  get: (key: string) => {
    return Taro.getStorage({ key });
  },

  // 异步移除存储
  remove: (key: string) => {
    return Taro.removeStorage({ key });
  },

  // 异步清空存储
  clear: () => {
    return Taro.clearStorage();
  },
};

// 导出平台信息
export const platformInfo = {
  current: getCurrentPlatform(),
  capabilities: getPlatformCapabilities(),
  config: getPlatformConfig(),
};
