// 全局可复用的防抖与节流工具
// 标准化 API，支持 Taro/React H5/小程序环境

import { rateLimitConfig } from '../config/rateLimit';

export type DebounceOptions = {
  wait?: number;
  maxWait?: number;
  leading?: boolean;
  trailing?: boolean;
};

export type ThrottleOptions = {
  interval?: number;
  limit?: number; // 在 interval 窗口内允许的最大调用次数
  leading?: boolean;
  trailing?: boolean;
};

export type CancellableFn<T extends (...args: any[]) => any> = ((
  ...args: Parameters<T>
) => ReturnType<T> | undefined) & {
  cancel: () => void;
  flush: () => ReturnType<T> | undefined;
};

// 防抖：在停止触发 wait 毫秒后执行（或根据 leading/trailing 配置）
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  options: DebounceOptions = {}
) {
  const {
    wait = rateLimitConfig.debounce.wait,
    maxWait = rateLimitConfig.debounce.maxWait,
    leading = rateLimitConfig.debounce.leading,
    trailing = rateLimitConfig.debounce.trailing,
  } = options;

  let timer: any = null;
  let lastInvokeTime = 0;
  let lastArgs: any[] | null = null;
  let lastThis: any;
  let result: any;
  let maxTimer: any = null;

  const invoke = (time: number) => {
    lastInvokeTime = time;
    const args = lastArgs!;
    const thisArg = lastThis;
    lastArgs = lastThis = null;
    result = fn.apply(thisArg, args);
    return result;
  };

  const startTimer = (callback: () => void, waitMs: number) => {
    timer && clearTimeout(timer);
    timer = setTimeout(callback, waitMs);
  };

  const trailingEdge = (time: number) => {
    timer = null;
    if (trailing && lastArgs) {
      return invoke(time);
    }
    lastArgs = lastThis = null;
    return result;
  };

  const leadingEdge = (time: number) => {
    if (leading) {
      result = invoke(time);
    }
    if (trailing) {
      startTimer(() => trailingEdge(Date.now()), wait);
    }
    return result;
  };

  const shouldInvoke = (time: number) => {
    if (lastArgs == null) return false;
    const sinceLastCall = time - (lastInvokeTime || 0);
    return maxWait !== undefined && maxWait !== null && maxWait >= 0
      ? sinceLastCall >= Math.min(wait, maxWait)
      : sinceLastCall >= wait;
  };

  const debounced: any = function (this: any, ...args: any[]) {
    const time = Date.now();
    lastArgs = args;
    lastThis = this;

    if (!lastInvokeTime) lastInvokeTime = time;

    // 处理 maxWait：保证在最长间隔后一定触发一次
    if (maxWait != null && maxWait >= 0) {
      if (!maxTimer) {
        maxTimer = setTimeout(() => {
          if (lastArgs) {
            leadingEdge(Date.now());
          }
          maxTimer && clearTimeout(maxTimer);
          maxTimer = null;
        }, maxWait);
      }
    }

    const isInvoking = shouldInvoke(time);

    if (timer == null) {
      return leadingEdge(time);
    }

    if (isInvoking) {
      startTimer(() => trailingEdge(Date.now()), wait);
    }

    return result;
  };

  debounced.cancel = () => {
    timer && clearTimeout(timer);
    timer = null;
    maxTimer && clearTimeout(maxTimer);
    maxTimer = null;
    lastArgs = lastThis = null;
  };

  debounced.flush = () => {
    if (timer) {
      clearTimeout(timer);
      return trailingEdge(Date.now());
    }
    return result;
  };

  return debounced as CancellableFn<T>;
}

// 节流：在 interval 窗口内限制调用次数（limit），可选 leading/trailing
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  options: ThrottleOptions = {}
) {
  const {
    interval = rateLimitConfig.throttle.interval,
    limit = rateLimitConfig.throttle.limit,
    leading = rateLimitConfig.throttle.leading,
    trailing = rateLimitConfig.throttle.trailing,
  } = options;

  let calls = 0;
  let windowStart = 0;
  let timer: any = null;
  let lastArgs: any[] | null = null;
  let lastThis: any;
  let result: any;
  let suppressed = false; // 是否出现被抑制（超过 limit）的调用

  const resetWindow = (now: number) => {
    windowStart = now;
    calls = 0;
    suppressed = false;
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  return function throttled(this: any, ...args: any[]) {
    const now = Date.now();
    if (now - windowStart >= interval) {
      resetWindow(now);
    }

    calls += 1;
    lastArgs = args;
    lastThis = this;

    const canCall = calls <= limit;

    if (canCall) {
      // 在限制范围内允许执行（支持 limit>1 的场景）
      if ((calls === 1 && leading) || calls > 1) {
        result = fn.apply(lastThis, lastArgs);
      }

      // 仅当存在被抑制的调用时，才在窗口结束时进行 trailing 补发
      if (trailing && suppressed) {
        if (timer) clearTimeout(timer);
        const remaining = interval - (now - windowStart);
        timer = setTimeout(() => {
          if (lastArgs) fn.apply(lastThis, lastArgs);
          resetWindow(Date.now());
        }, Math.max(0, remaining));
      }

      return result;
    }

    // 超过 limit：记录被抑制，必要时安排 trailing
    suppressed = true;
    if (trailing) {
      if (timer) clearTimeout(timer);
      const remaining = interval - (now - windowStart);
      timer = setTimeout(() => {
        if (lastArgs) fn.apply(lastThis, lastArgs);
        resetWindow(Date.now());
      }, Math.max(0, remaining));
    }

    return result;
  } as T;
}
