import Taro from '@tarojs/taro'
import { platform } from './platform'

// 平台适配的存储接口
interface PlatformStorage {
  getItem: (key: string) => string | null
  setItem: (key: string, value: string) => void
  removeItem: (key: string) => void
  clear: () => void
}

// 创建平台适配的存储实现
const createPlatformStorage = (): PlatformStorage => {
  // 在小程序环境中使用 Taro 的存储 API
  if (platform.isMiniProgram()) {
    return {
      getItem: (key: string) => {
        try {
          return Taro.getStorageSync(key) || null
        } catch (error) {
          console.warn('Failed to get storage item:', key, error)
          return null
        }
      },
      setItem: (key: string, value: string) => {
        try {
          Taro.setStorageSync(key, value)
        } catch (error) {
          console.warn('Failed to set storage item:', key, error)
        }
      },
      removeItem: (key: string) => {
        try {
          Taro.removeStorageSync(key)
        } catch (error) {
          console.warn('Failed to remove storage item:', key, error)
        }
      },
      clear: () => {
        try {
          Taro.clearStorageSync()
        } catch (error) {
          console.warn('Failed to clear storage:', error)
        }
      }
    }
  }

  // 在 H5 环境中使用 localStorage
  if (platform.isH5() && typeof window !== 'undefined' && window.localStorage) {
    return {
      getItem: (key: string) => {
        try {
          return window.localStorage.getItem(key)
        } catch (error) {
          console.warn('Failed to get localStorage item:', key, error)
          return null
        }
      },
      setItem: (key: string, value: string) => {
        try {
          window.localStorage.setItem(key, value)
        } catch (error) {
          console.warn('Failed to set localStorage item:', key, error)
        }
      },
      removeItem: (key: string) => {
        try {
          window.localStorage.removeItem(key)
        } catch (error) {
          console.warn('Failed to remove localStorage item:', key, error)
        }
      },
      clear: () => {
        try {
          window.localStorage.clear()
        } catch (error) {
          console.warn('Failed to clear localStorage:', error)
        }
      }
    }
  }

  // 降级方案：内存存储
  const memoryStorage = new Map<string, string>()
  console.warn('Using memory storage as fallback')
  
  return {
    getItem: (key: string) => memoryStorage.get(key) || null,
    setItem: (key: string, value: string) => {
      memoryStorage.set(key, value)
    },
    removeItem: (key: string) => {
      memoryStorage.delete(key)
    },
    clear: () => {
      memoryStorage.clear()
    }
  }
}

// 导出平台适配的存储实例
export const platformStorage = createPlatformStorage()

// 为 Zustand 提供的存储适配器
export const createZustandStorage = () => ({
  getItem: (name: string) => {
    const value = platformStorage.getItem(name)
    return value ? JSON.parse(value) : null
  },
  setItem: (name: string, value: any) => {
    platformStorage.setItem(name, JSON.stringify(value))
  },
  removeItem: (name: string) => {
    platformStorage.removeItem(name)
  }
})

// 便捷的存储工具函数
export const storage = {
  // 获取存储值并解析 JSON
  get: <T = any>(key: string, defaultValue?: T): T | null => {
    try {
      const value = platformStorage.getItem(key)
      return value ? JSON.parse(value) : defaultValue || null
    } catch (error) {
      console.warn('Failed to parse storage value:', key, error)
      return defaultValue || null
    }
  },

  // 设置存储值，自动序列化 JSON
  set: (key: string, value: any): void => {
    try {
      platformStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.warn('Failed to set storage value:', key, error)
    }
  },

  // 移除存储值
  remove: (key: string): void => {
    platformStorage.removeItem(key)
  },

  // 清空所有存储
  clear: (): void => {
    platformStorage.clear()
  },

  // 检查键是否存在
  has: (key: string): boolean => {
    return platformStorage.getItem(key) !== null
  },

  // 获取所有键（仅在支持的平台）
  keys: (): string[] => {
    if (platform.isH5() && typeof window !== 'undefined' && window.localStorage) {
      return Object.keys(window.localStorage)
    }
    
    // 小程序环境下无法直接获取所有键，返回空数组
    console.warn('Getting all storage keys is not supported in mini-program environment')
    return []
  }
}