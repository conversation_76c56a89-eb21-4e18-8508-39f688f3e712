import Taro from '@tarojs/taro';
import type { Breakpoints, DeviceType } from '../types';

// 响应式断点配置
export const breakpoints: Breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  xxl: 1536,
};

// 获取设备类型
export const getDeviceType = (width: number): DeviceType => {
  if (width < breakpoints.md) return 'mobile';
  if (width < breakpoints.lg) return 'tablet';
  return 'desktop';
};

// 判断是否为移动设备
export const isMobile = (width?: number): boolean => {
  const screenWidth = width || getScreenWidth();
  return screenWidth < breakpoints.md;
};

// 判断是否为平板设备
export const isTablet = (width?: number): boolean => {
  const screenWidth = width || getScreenWidth();
  return screenWidth >= breakpoints.md && screenWidth < breakpoints.lg;
};

// 判断是否为桌面设备
export const isDesktop = (width?: number): boolean => {
  const screenWidth = width || getScreenWidth();
  return screenWidth >= breakpoints.lg;
};

// 获取屏幕宽度
export const getScreenWidth = (): number => {
  try {
    const systemInfo = Taro.getSystemInfoSync();
    return systemInfo.screenWidth;
  } catch (error) {
    return 375; // 默认移动设备宽度
  }
};

// 获取屏幕高度
export const getScreenHeight = (): number => {
  try {
    const systemInfo = Taro.getSystemInfoSync();
    return systemInfo.screenHeight;
  } catch (error) {
    return 667; // 默认移动设备高度
  }
};

// 获取状态栏高度
export const getStatusBarHeight = (): number => {
  try {
    const systemInfo = Taro.getSystemInfoSync();
    return systemInfo.statusBarHeight || 0;
  } catch (error) {
    return 0;
  }
};

// 获取导航栏高度
export const getNavigationBarHeight = (): number => {
  try {
    if (process.env.TARO_ENV === 'weapp') {
      const systemInfo = Taro.getSystemInfoSync();
      const menuButton = Taro.getMenuButtonBoundingClientRect();
      return (
        (menuButton.top - (systemInfo.statusBarHeight || 0)) * 2 +
        menuButton.height
      );
    }
    return 44; // 默认导航栏高度
  } catch (error) {
    return 44;
  }
};

// 获取安全区域
export const getSafeArea = () => {
  try {
    const systemInfo = Taro.getSystemInfoSync();
    return {
      top: systemInfo.safeArea?.top || 0,
      bottom: systemInfo.safeArea?.bottom || systemInfo.screenHeight,
      left: systemInfo.safeArea?.left || 0,
      right: systemInfo.safeArea?.right || systemInfo.screenWidth,
      width: systemInfo.safeArea?.width || systemInfo.screenWidth,
      height: systemInfo.safeArea?.height || systemInfo.screenHeight,
    };
  } catch (error) {
    const width = getScreenWidth();
    const height = getScreenHeight();
    return {
      top: 0,
      bottom: height,
      left: 0,
      right: width,
      width,
      height,
    };
  }
};

// px转rpx（小程序专用）
export const pxToRpx = (px: number): number => {
  const screenWidth = getScreenWidth();
  return (px * 750) / screenWidth;
};

// rpx转px（小程序专用）
export const rpxToPx = (rpx: number): number => {
  const screenWidth = getScreenWidth();
  return (rpx * screenWidth) / 750;
};

// rem转px
export const remToPx = (rem: number): number => {
  const baseFontSize = isMobile() ? 14 : isTablet() ? 16 : 18;
  return rem * baseFontSize;
};

// px转rem
export const pxToRem = (px: number): number => {
  const baseFontSize = isMobile() ? 14 : isTablet() ? 16 : 18;
  return px / baseFontSize;
};

// 获取响应式值
export const getResponsiveValue = <T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  xxl?: T;
}): T | undefined => {
  const screenWidth = getScreenWidth();

  if (screenWidth >= breakpoints.xxl && values.xxl !== undefined)
    return values.xxl;
  if (screenWidth >= breakpoints.xl && values.xl !== undefined)
    return values.xl;
  if (screenWidth >= breakpoints.lg && values.lg !== undefined)
    return values.lg;
  if (screenWidth >= breakpoints.md && values.md !== undefined)
    return values.md;
  if (screenWidth >= breakpoints.sm && values.sm !== undefined)
    return values.sm;
  if (values.xs !== undefined) return values.xs;

  // 返回最后一个定义的值
  return (
    values.xxl || values.xl || values.lg || values.md || values.sm || values.xs
  );
};

// 媒体查询工具
export const mediaQuery = {
  up: (breakpoint: keyof Breakpoints) =>
    `(min-width: ${breakpoints[breakpoint]}px)`,
  down: (breakpoint: keyof Breakpoints) =>
    `(max-width: ${breakpoints[breakpoint] - 1}px)`,
  between: (start: keyof Breakpoints, end: keyof Breakpoints) =>
    `(min-width: ${breakpoints[start]}px) and (max-width: ${
      breakpoints[end] - 1
    }px)`,
  only: (breakpoint: keyof Breakpoints) => {
    const keys = Object.keys(breakpoints) as (keyof Breakpoints)[];
    const index = keys.indexOf(breakpoint);
    const nextBreakpoint = keys[index + 1];

    if (nextBreakpoint) {
      return mediaQuery.between(breakpoint, nextBreakpoint);
    }
    return mediaQuery.up(breakpoint);
  },
};

// 响应式类名生成器
export const generateResponsiveClasses = (
  baseClass: string,
  values: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    xxl?: string;
  }
): string => {
  const classes: string[] = [];

  Object.entries(values).forEach(([breakpoint, value]) => {
    if (value) {
      if (breakpoint === 'xs') {
        classes.push(`${baseClass}-${value}`);
      } else {
        classes.push(`${breakpoint}:${baseClass}-${value}`);
      }
    }
  });

  return classes.join(' ');
};

// 响应式样式对象生成器
export const generateResponsiveStyles = (
  property: string,
  values: {
    xs?: string | number;
    sm?: string | number;
    md?: string | number;
    lg?: string | number;
    xl?: string | number;
    xxl?: string | number;
  }
): Record<string, any> => {
  const styles: Record<string, any> = {};

  Object.entries(values).forEach(([breakpoint, value]) => {
    if (value !== undefined) {
      if (breakpoint === 'xs') {
        styles[property] = value;
      } else {
        const mediaQueryKey = `@media ${mediaQuery.up(
          breakpoint as keyof Breakpoints
        )}`;
        if (!styles[mediaQueryKey]) {
          styles[mediaQueryKey] = {};
        }
        styles[mediaQueryKey][property] = value;
      }
    }
  });

  return styles;
};
