import { Image, Input, ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import api from '../../services/api';
import './index.scss';
import {
  ALPHABET_LETTERS,
  CryptoElevatorProps,
  CryptoGroup,
  CryptoItem,
} from './types';

const CryptoElevator: React.FC<CryptoElevatorProps> = ({
  visible,
  onClose,
  onConfirm,
  mode = 'single',
  maxSelection = 4,
  preSelectedCoins = [],
  title = '币种选择',
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCoins, setSelectedCoins] =
    useState<CryptoItem[]>(preSelectedCoins);
  const [allCoins, setAllCoins] = useState<CryptoItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeLetterIndex, setActiveLetterIndex] = useState(0);

  const scrollViewRef = useRef<any>(null);

  // 获取币种数据
  const fetchCoins = async (key?: string) => {
    try {
      setLoading(true);
      const response = await api.crypto.search({
        key,
      });

      // 检查响应数据结构
      // 由于响应拦截器的处理，这里的 response 可能直接是 data 部分
      const responseAny = response as any;

      if (
        responseAny &&
        responseAny.results &&
        Array.isArray(responseAny.results)
      ) {
        // 直接是数据部分
        setAllCoins(responseAny.results);
      } else if (response && response.data && response.data.results) {
        // 完整的响应结构
        setAllCoins(response.data.results);
      } else {
        console.warn('API 响应格式不正确:', response);
        Taro.showToast({
          title: '数据格式错误',
          icon: 'none',
        });
      }
    } catch (error) {
      console.error('获取币种数据失败:', error);
      Taro.showToast({
        title: '获取数据失败',
        icon: 'none',
      });
    } finally {
      setLoading(false);
    }
  };

  // 按字母分组币种
  const groupedCoins = useMemo(() => {
    const groups: CryptoGroup[] = [];
    const groupMap = new Map<string, CryptoItem[]>();

    // 过滤和分组
    allCoins.forEach(coin => {
      const firstLetter = coin.name.charAt(0).toUpperCase();
      const letter = ALPHABET_LETTERS.includes(firstLetter as any)
        ? firstLetter
        : '#';

      if (!groupMap.has(letter)) {
        groupMap.set(letter, []);
      }
      groupMap.get(letter)!.push(coin);
    });

    // 转换为数组并排序
    ALPHABET_LETTERS.forEach(letter => {
      if (groupMap.has(letter)) {
        groups.push({
          letter,
          coins: groupMap
            .get(letter)!
            .sort((a, b) => a.name.localeCompare(b.name)),
        });
      }
    });

    // 添加其他字符组
    if (groupMap.has('#')) {
      groups.push({
        letter: '#',
        coins: groupMap.get('#')!.sort((a, b) => a.name.localeCompare(b.name)),
      });
    }

    return groups;
  }, [allCoins]);

  // 处理币种选择
  const handleCoinSelect = (coin: CryptoItem) => {
    if (mode === 'single') {
      setSelectedCoins([coin]);
    } else {
      const isSelected = selectedCoins.some(c => c.id === coin.id);

      if (isSelected) {
        setSelectedCoins(prev => prev.filter(c => c.id !== coin.id));
      } else {
        if (selectedCoins.length >= maxSelection) {
          Taro.showToast({
            title: `最多只能选择${maxSelection}个币种`,
            icon: 'none',
          });
          return;
        }
        setSelectedCoins(prev => [...prev, coin]);
      }
    }
  };

  // 处理字母导航点击
  const handleAlphabetClick = (letter: string, index: number) => {
    setActiveLetterIndex(index);

    // 滚动到对应字母组
    const targetGroup = groupedCoins.find(group => group.letter === letter);
    if (targetGroup && scrollViewRef.current) {
      // 这里可以实现滚动到指定位置的逻辑
      // Taro 的 ScrollView 可以使用 scrollIntoView 属性
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    fetchCoins(value);
  };

  // 处理确认
  const handleConfirm = () => {
    if (selectedCoins.length === 0) {
      Taro.showToast({
        title: '请选择币种',
        icon: 'none',
      });
      return;
    }

    onConfirm(selectedCoins);
    onClose();
  };

  // 检查币种是否被选中
  const isCoinSelected = (coinId: string) => {
    return selectedCoins.some(c => c.id === coinId);
  };

  // 初始化数据
  useEffect(() => {
    if (visible) {
      fetchCoins();
      setSelectedCoins(preSelectedCoins);
      setSearchQuery('');
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <View className='crypto-elevator'>
      <View className='crypto-elevator__mask' onClick={onClose} />

      <View className='crypto-elevator__content'>
        {/* 头部 */}
        <View className='crypto-elevator__header'>
          <Text className='title'>{title}</Text>

          <View className='crypto-elevator__search'>
            <Input
              className='search-input'
              placeholder='搜索币种名称或符号'
              value={searchQuery}
              onInput={e => handleSearch(e.detail.value)}
            />
            <Text className='search-icon'>🔍</Text>
          </View>
        </View>

        {/* 主体内容 */}
        <View className='crypto-elevator__body'>
          <ScrollView
            ref={scrollViewRef || (undefined as any)}
            className='crypto-elevator__list'
            scrollY
            enhanced
            showScrollbar={false}
          >
            {loading ? (
              <View className='crypto-elevator__loading'>
                <Text>加载中...</Text>
              </View>
            ) : groupedCoins.length === 0 ? (
              <View className='crypto-elevator__empty'>
                <Text className='empty-icon'>🔍</Text>
                <Text className='empty-text'>
                  {searchQuery ? '未找到相关币种' : '暂无数据'}
                </Text>
              </View>
            ) : (
              groupedCoins.map(group => (
                <View key={group.letter} className='crypto-elevator__group'>
                  <Text className='group-title'>{group.letter}</Text>
                  {group.coins.map(coin => (
                    <View
                      key={coin.id}
                      className={`crypto-elevator__item ${
                        isCoinSelected(coin.id)
                          ? 'crypto-elevator__item--selected'
                          : ''
                      }`}
                      onClick={() => handleCoinSelect(coin)}
                    >
                      <Image
                        className='coin-icon'
                        src={coin.image}
                        mode='aspectFill'
                      />
                      <View className='coin-info'>
                        <Text className='coin-name'>{coin.name}</Text>
                        <Text className='coin-symbol'>{coin.symbol}</Text>
                      </View>
                      <View
                        className={`selection-indicator ${
                          isCoinSelected(coin.id)
                            ? 'selection-indicator--selected'
                            : ''
                        }`}
                      />
                    </View>
                  ))}
                </View>
              ))
            )}
          </ScrollView>

          {/* 字母导航 */}
          <View className='crypto-elevator__alphabet'>
            {ALPHABET_LETTERS.map((letter, index) => (
              <Text
                key={letter}
                className={`alphabet-item ${
                  index === activeLetterIndex ? 'alphabet-item--active' : ''
                }`}
                onClick={() => handleAlphabetClick(letter, index)}
              >
                {letter}
              </Text>
            ))}
          </View>
        </View>

        {/* 底部操作 */}
        <View className='crypto-elevator__footer'>
          <View className='btn btn--cancel' onClick={onClose}>
            取消
          </View>
          <View
            className={`btn btn--confirm ${
              selectedCoins.length === 0 ? 'btn--confirm:disabled' : ''
            }`}
            onClick={handleConfirm}
          >
            确定
            {mode === 'multiple' && selectedCoins.length > 0 && (
              <Text>
                {' '}
                ({selectedCoins.length}/{maxSelection})
              </Text>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

export default CryptoElevator;
