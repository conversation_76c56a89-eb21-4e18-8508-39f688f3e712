import Taro from '@tarojs/taro';
import { CryptoItem, STORAGE_KEYS } from './types';

/**
 * 获取已选择的币种列表（多选模式）
 */
export const getSelectedCoins = (): CryptoItem[] => {
  try {
    const stored = Taro.getStorageSync(STORAGE_KEYS.SELECTED_COINS);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('获取已选择币种失败:', error);
    return [];
  }
};

/**
 * 保存已选择的币种列表（多选模式）
 */
export const saveSelectedCoins = (coins: CryptoItem[]): void => {
  try {
    Taro.setStorageSync(STORAGE_KEYS.SELECTED_COINS, JSON.stringify(coins));
  } catch (error) {
    console.error('保存已选择币种失败:', error);
  }
};

/**
 * 获取分析页面选择的币种（单选模式）
 */
export const getAnalysisSelectedCoin = (): CryptoItem | null => {
  try {
    const stored = Taro.getStorageSync(STORAGE_KEYS.ANALYSIS_SELECTED_COIN);
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.error('获取分析选择币种失败:', error);
    return null;
  }
};

/**
 * 保存分析页面选择的币种（单选模式）
 */
export const saveAnalysisSelectedCoin = (coin: CryptoItem | null): void => {
  try {
    if (coin) {
      Taro.setStorageSync(STORAGE_KEYS.ANALYSIS_SELECTED_COIN, JSON.stringify(coin));
    } else {
      Taro.removeStorageSync(STORAGE_KEYS.ANALYSIS_SELECTED_COIN);
    }
  } catch (error) {
    console.error('保存分析选择币种失败:', error);
  }
};

/**
 * 添加币种到已选择列表
 */
export const addCoinToSelected = (coin: CryptoItem, maxCount = 4): boolean => {
  const currentCoins = getSelectedCoins();
  
  // 检查是否已存在
  if (currentCoins.some(c => c.id === coin.id)) {
    return false;
  }
  
  // 检查是否超过最大数量
  if (currentCoins.length >= maxCount) {
    return false;
  }
  
  const newCoins = [...currentCoins, coin];
  saveSelectedCoins(newCoins);
  return true;
};

/**
 * 从已选择列表中移除币种
 */
export const removeCoinFromSelected = (coinId: string): void => {
  const currentCoins = getSelectedCoins();
  const newCoins = currentCoins.filter(c => c.id !== coinId);
  saveSelectedCoins(newCoins);
};

/**
 * 检查币种是否已被选择
 */
export const isCoinSelected = (coinId: string): boolean => {
  const selectedCoins = getSelectedCoins();
  return selectedCoins.some(c => c.id === coinId);
};

/**
 * 清空所有已选择的币种
 */
export const clearSelectedCoins = (): void => {
  saveSelectedCoins([]);
};
