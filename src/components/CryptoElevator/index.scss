// 加密货币电梯组件样式
.crypto-elevator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  &__mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    animation: fade-in 0.3s ease;
  }

  &__content {
    position: relative;
    width: 100%;
    max-height: 85vh;
    // 确保在横屏模式下也有最小高度
    min-height: 300px;
    background: #1a1a1a;
    border-radius: 1.25rem 1.25rem 0 0;
    animation: slide-up 0.3s ease;
    display: flex;
    flex-direction: column;
    // 确保底部按钮不被遮挡
    padding-bottom: env(safe-area-inset-bottom);
  }

  &__header {
    padding: 1.25rem 1rem 1rem;
    border-bottom: 0.0625rem solid #333;

    .title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #ffffff;
      text-align: center;
      line-height: 1.5;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &__search {
    position: relative;

    .search-input {
      width: 100%;
      height: 2.75rem;
      background: #2a2a2a;
      border: 0.0625rem solid #444;
      border-radius: 0.75rem;
      padding: 0 1rem;
      font-size: 1rem;
      color: #ffffff;
      display: flex;
      align-items: center;
      line-height: 2.75rem;

      &::placeholder {
        color: #888;
      }

      &:focus {
        border-color: #007aff;
        outline: none;
      }
    }

    .search-icon {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
      color: #888;
      font-size: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &__body {
    flex: 1;
    display: flex;
    overflow: hidden;
    // 确保内容区域不会与底部按钮重叠
    min-height: 0;
  }

  &__list {
    flex: 1;
    overflow-y: auto;
    padding: 0 1rem;
    // 确保列表可以正确滚动
    min-height: 0;
    // 为底部留出空间
    padding-bottom: 1rem;
  }

  &__group {
    margin-bottom: 0.5rem;

    .group-title {
      font-size: 0.875rem;
      font-weight: 600;
      color: #888;
      padding: 0.75rem 0 0.5rem;
      position: sticky;
      top: 0;
      background: #1a1a1a;
      z-index: 10;
      display: flex;
      align-items: center;
      line-height: 1.5;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 0.0625rem solid #2a2a2a;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    &--selected {
      background: rgba(0, 122, 255, 0.1);
      border-color: #007aff;
    }

    .coin-icon {
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      margin-right: 0.75rem;
      background: #333;
    }

    .coin-info {
      flex: 1;

      .coin-name {
        font-size: 1rem;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 0.125rem;
        line-height: 1.5;
        display: flex;
        align-items: center;
      }

      .coin-symbol {
        font-size: 0.75rem;
        color: #888;
        text-transform: uppercase;
        line-height: 1.5;
        display: flex;
        align-items: center;
      }
    }

    .selection-indicator {
      width: 1.25rem;
      height: 1.25rem;
      border: 0.125rem solid #444;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &--selected {
        background: #007aff;
        border-color: #007aff;

        &::after {
          content: '✓';
          color: #ffffff;
          font-size: 0.75rem;
          font-weight: bold;
          line-height: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  &__alphabet {
    width: 1.5rem;
    padding: 1rem 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);

    .alphabet-item {
      font-size: 0.625rem;
      color: #888;
      padding: 0.125rem 0;
      cursor: pointer;
      transition: color 0.2s ease;
      user-select: none;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;

      &:hover,
      &--active {
        color: #007aff;
        font-weight: bold;
      }
    }
  }

  &__footer {
    padding: 1rem;
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
    border-top: 0.0625rem solid #333;
    display: flex;
    gap: 0.75rem;
    background: #1a1a1a;
    // 确保按钮区域始终可见
    flex-shrink: 0;

    .btn {
      flex: 1;
      height: 2.75rem;
      border-radius: 0.75rem;
      font-size: 1rem;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      line-height: 1;
      &--cancel {
        background: #2a2a2a;
        color: #ffffff;

        &:hover {
          background: #333;
        }
      }

      &--confirm {
        background: #007aff;
        color: #ffffff;

        &:hover {
          background: #0056cc;
        }

        &:disabled {
          background: #444;
          color: #888;
          cursor: not-allowed;
        }
      }
    }
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2.5rem;
    color: #888;
    font-size: 1rem;
    line-height: 1.5;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2.5rem;
    color: #888;

    .empty-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
      line-height: 1;
    }

    .empty-text {
      font-size: 1rem;
      line-height: 1.5;
      text-align: center;
    }
  }
}

// 动画
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

// 响应式适配
@media (max-width: 30rem) {
  .crypto-elevator {
    &__content {
      max-height: 85vh;
    }

    &__header {
      padding: 1rem 0.75rem 0.75rem;

      .title {
        font-size: 1rem;
        margin-bottom: 0.75rem;
      }
    }

    &__search {
      .search-input {
        height: 2.5rem;
        font-size: 0.875rem;
        line-height: 2.5rem;
      }
    }

    &__list {
      padding: 0 0.75rem;
    }

    &__item {
      padding: 0.625rem 0;

      .coin-icon {
        width: 1.75rem;
        height: 1.75rem;
        margin-right: 0.625rem;
      }

      .coin-info {
        .coin-name {
          font-size: 0.875rem;
        }

        .coin-symbol {
          font-size: 0.6875rem;
        }
      }
    }

    &__footer {
      padding: 0.75rem;
      padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));

      .btn {
        height: 2.5rem;
        font-size: 0.875rem;
      }
    }
  }
}
