// 加密货币电梯组件相关类型定义

export interface CryptoItem {
  id: string;
  symbol: string;
  name: string;
  image: string;
}

export interface CryptoGroup {
  letter: string;
  coins: CryptoItem[];
}

export interface CryptoElevatorProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (selectedCoins: CryptoItem[]) => void;
  mode?: 'single' | 'multiple'; // 单选或多选模式
  maxSelection?: number; // 最大选择数量，仅在多选模式下有效
  preSelectedCoins?: CryptoItem[]; // 预选的币种
  title?: string; // 弹窗标题
}

export interface CryptoElevatorState {
  searchQuery: string;
  selectedCoins: CryptoItem[];
  allCoins: CryptoItem[];
  groupedCoins: CryptoGroup[];
  loading: boolean;
  activeLetterIndex: number;
}

// localStorage 存储的键名
export const STORAGE_KEYS = {
  SELECTED_COINS: 'selected_coins',
  ANALYSIS_SELECTED_COIN: 'analysis_selected_coin',
} as const;

// 字母导航列表
export const ALPHABET_LETTERS = [
  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
  'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
] as const;
