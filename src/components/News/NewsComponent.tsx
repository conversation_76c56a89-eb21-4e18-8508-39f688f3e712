import { Button, Tabs } from '@nutui/nutui-react-taro';
import { ScrollView, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { loadingSelectors, useLoadingStore } from '../../stores/loadingStore';
import {
  NEWS_CATEGORIES,
  NEWS_LOADING_IDS,
  NewsCategory,
  newsSelectors,
  useNewsStore,
} from '../../stores/news-fetch';
import { navigation } from '../../utils/platform';
import Grid from '../Layout/Grid';
import LazyImage from '../LazyLoad/LazyImage';
import { SkeletonListItem } from '../Skeleton/Skeleton';
import './NewsComponent.scss';

interface NewsComponentProps {
  /** 是否显示导航栏 */
  showNavigation?: boolean;
  /** 是否显示分类tabs */
  showTabs?: boolean;
  /** 最大显示数量（用于首页预览） */
  maxItems?: number;
  /** 容器高度 */
  containerHeight?: string;
  /** 是否启用下拉刷新 */
  enableRefresh?: boolean;
  /** 是否启用无限滚动 */
  enableInfiniteScroll?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 点击新闻项的回调 */
  onNewsClick?: (newsItem: any) => void;
  /** 自定义样式覆写类名 */
  customStyles?: {
    /** 主容器样式类名 */
    container?: string;
    /** Tabs容器样式类名 */
    tabsContainer?: string;
    /** Tabs样式类名 */
    tabs?: string;
    /** VirtualList容器样式类名 */
    listContainer?: string;
    /** VirtualList样式类名 */
    virtualList?: string;
    /** 新闻项样式类名 */
    newsItem?: string;
    /** 加载状态样式类名 */
    loadingState?: string;
  };
}

/**
 * 新闻组件 - 基于NutUI的Tabs和VirtualList重新封装
 * 支持粘性布局、无限加载、多种使用场景
 */
const NewsComponent: React.FC<NewsComponentProps> = ({
  showTabs = true,
  maxItems,
  containerHeight = '100vh',
  enableRefresh = true,
  enableInfiniteScroll = true,
  className = '',
  onNewsClick,
  customStyles = {},
}) => {
  // Store状态
  const currentCategory = useNewsStore(newsSelectors.getCurrentCategory());
  const currentNews = useNewsStore(newsSelectors.getCurrentCategoryNews());
  const newsError = useNewsStore(newsSelectors.getNewsError());
  const hasMore = useNewsStore(newsSelectors.getHasMore(currentCategory));
  const isLoadingMore = useNewsStore(newsSelectors.getIsLoadingMore());

  // Store方法
  const fetchNewsByCategory = useNewsStore(state => state.fetchNewsByCategory);
  const refreshNews = useNewsStore(state => state.refreshNews);
  const loadMoreNews = useNewsStore(state => state.loadMoreNews);
  const setCurrentCategory = useNewsStore(state => state.setCurrentCategory);
  const clearError = useNewsStore(state => state.clearError);

  // 加载状态
  const newsLoading = useLoadingStore(
    loadingSelectors.getLoadingById(NEWS_LOADING_IDS.NEWS_CATEGORY)
  );

  // 粘性布局状态
  const [isSticky, setIsSticky] = useState(false);
  const stickyOffsetTop = 66; // 导航栏高度

  // 监听滚动，实现粘性布局 - 使用Taro跨平台API
  useEffect(() => {
    if (!showTabs) return;

    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        // 使用Taro的跨平台API获取滚动位置
        Taro.createSelectorQuery()
          .select('.news-component')
          .boundingClientRect()
          .exec(res => {
            if (res && res[0]) {
              const rect = res[0];
              // 当组件顶部距离视口顶部小于等于粘性偏移量时，激活粘性状态
              const shouldBeSticky = rect.top <= stickyOffsetTop;

              if (shouldBeSticky !== isSticky) {
                setIsSticky(shouldBeSticky);
              }
            }
            ticking = false;
          });
        ticking = true;
      }
    };

    // 在H5环境下使用window scroll事件，在小程序环境下依赖页面级别的scroll监听
    if (process.env.TARO_ENV === 'h5') {
      const throttledHandleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(handleScroll);
          ticking = true;
        }
      };

      window.addEventListener('scroll', throttledHandleScroll, {
        passive: true,
      });

      return () => {
        window.removeEventListener('scroll', throttledHandleScroll);
      };
    }

    // 小程序环境下的处理在页面级别完成
    return () => {};
  }, [isSticky, showTabs, stickyOffsetTop]);

  // 初始化加载新闻
  useEffect(() => {
    let isSubscribed = true;

    const loadNews = async () => {
      try {
        await fetchNewsByCategory(currentCategory, 1, false);
      } catch (error) {
        if (isSubscribed) {
          console.error('获取新闻失败:', error);
        }
      }
    };

    loadNews();

    return () => {
      isSubscribed = false;
    };
  }, [currentCategory, fetchNewsByCategory]);

  // 处理分类切换
  const handleCategoryChange = useCallback(
    (category: NewsCategory) => {
      if (category === currentCategory) return;
      setCurrentCategory(category);
    },
    [currentCategory, setCurrentCategory]
  );

  // 处理加载更多
  const handleLoadMore = useCallback(async () => {
    if (!enableInfiniteScroll || !hasMore || isLoadingMore) return;

    try {
      await loadMoreNews(currentCategory);
    } catch (error) {
      console.error('加载更多失败:', error);
    }
  }, [
    enableInfiniteScroll,
    hasMore,
    isLoadingMore,
    loadMoreNews,
    currentCategory,
  ]);

  // 跳转到新闻详情页
  const navigateToNewsDetail = useCallback((url: string) => {
    navigation.navigateTo(
      `/pages/news-details/index?url=${encodeURIComponent(url)}`
    );
  }, []);

  // 处理新闻项点击
  const handleNewsClick = useCallback(
    (newsItem: any) => {
      if (onNewsClick) {
        onNewsClick(newsItem);
      } else {
        navigateToNewsDetail(newsItem.url);
      }
    },
    [onNewsClick, navigateToNewsDetail]
  );

  // 渲染新闻项
  const renderNewsItem = useCallback(
    (item: any, index: number) => {
      // 直接使用新闻数据，不需要item.data
      const newsItem = item;
      return (
        <View
          key={`news-${newsItem.id}-${index}`}
          className={`virtual-news-item news-item-wrapper ${
            customStyles.newsItem || ''
          }`}
          onClick={() => handleNewsClick(newsItem)}
        >
          <View className='news-item'>
            <Grid container spacing={2}>
              <Grid item xs={8}>
                <View className='news-content'>
                  <Text className='news-title'>{newsItem.title}</Text>
                  <Text className='news-summary'>{newsItem.description}</Text>
                  <View className='news-meta'>
                    <Text className='news-source'>{newsItem.source}</Text>
                    <Text className='news-date'>
                      {new Date(newsItem.publishedAt).toLocaleDateString()}
                    </Text>
                  </View>
                </View>
              </Grid>
              <Grid item xs={4}>
                <LazyImage
                  src={newsItem.imageUrl}
                  alt={newsItem.title}
                  aspectRatio={16 / 9}
                  className='news-image'
                />
              </Grid>
            </Grid>
          </View>
        </View>
      );
    },
    [handleNewsClick]
  );

  // 渲染骨架屏项
  const renderSkeletonItem = useCallback((_item: any, index: number) => {
    return (
      <View key={`skeleton-${index}`} className='virtual-news-item'>
        <SkeletonListItem showAvatar showSecondary />
      </View>
    );
  }, []);

  // 获取要显示的新闻列表数据
  const displayNews = useMemo(() => {
    const news = maxItems ? currentNews.slice(0, maxItems) : currentNews;
    return news; // 直接返回新闻数组
  }, [currentNews, maxItems]);

  // 骨架屏数据
  const skeletonData = useMemo(() => {
    return Array.from({ length: maxItems || 6 }); // 简化为空数组
  }, [maxItems]);

  // 处理加载更多 - 用于ScrollView的onScrollToLower
  const handleScrollToLower = useCallback(() => {
    if (enableInfiniteScroll && hasMore && !isLoadingMore) {
      handleLoadMore();
    }
  }, [enableInfiniteScroll, hasMore, isLoadingMore, handleLoadMore]);

  // 错误处理
  if (newsError) {
    return (
      <View className={`news-component news-error ${className}`}>
        <Text className='error-message'>{newsError}</Text>
        <Button
          type='primary'
          size='small'
          onClick={() => {
            clearError();
            refreshNews(currentCategory);
          }}
        >
          重试
        </Button>
      </View>
    );
  }

  return (
    <View
      className={`news-component news-component--enhanced ${
        isSticky ? 'news-component--sticky' : ''
      } ${className} ${customStyles.container || ''}`}
      style={{ height: containerHeight }}
    >
      {/* 分类选择 Tabs */}
      {showTabs && (
        <View
          className={`news-categories sticky-tabs-container ${
            isSticky ? 'news-categories--sticky' : ''
          } ${customStyles.tabsContainer || ''}`}
          style={{
            position: isSticky ? 'fixed' : 'relative',
            top: isSticky ? `${stickyOffsetTop}px` : 'auto',
            left: 0,
            right: 0,
            zIndex: 100,
          }}
        >
          <Tabs
            value={currentCategory}
            onChange={value => handleCategoryChange(value as NewsCategory)}
            className={`news-tabs news-tabs--enhanced ${
              customStyles.tabs || ''
            }`}
          >
            {Object.entries(NEWS_CATEGORIES).map(([key, config]) => (
              <Tabs.TabPane key={key} title={config.name} value={key} />
            ))}
          </Tabs>
        </View>
      )}

      {/* 新闻列表 - 使用ScrollView替代VirtualList */}
      <View
        className={`news-list-container news-list-container--scroll ${
          customStyles.listContainer || ''
        }`}
      >
        <ScrollView
          className={`news-scroll-view ${customStyles.virtualList || ''}`}
          scrollY
          style={{ height: '600px' }}
          onScrollToLower={
            enableInfiniteScroll ? handleScrollToLower : undefined
          }
          lowerThreshold={100}
        >
          {newsLoading?.type === 'loading' && displayNews.length === 0 ? (
            // 骨架屏列表
            <View className='news-list'>
              {skeletonData.map((_, index) => (
                <View key={`skeleton-${index}`} className='virtual-news-item'>
                  <SkeletonListItem showAvatar showSecondary />
                </View>
              ))}
            </View>
          ) : (
            // 新闻列表
            <View className='news-list'>
              {displayNews.map((item, index) => renderNewsItem(item, index))}
            </View>
          )}

          {/* 加载更多指示器 */}
          {enableInfiniteScroll && isLoadingMore && (
            <View
              className={`loading-more loading-more--enhanced ${
                customStyles.loadingState || ''
              }`}
            >
              <Text>加载中...</Text>
            </View>
          )}

          {/* 没有更多数据提示 */}
          {enableInfiniteScroll &&
            displayNews.length > 0 &&
            !hasMore &&
            !isLoadingMore && (
              <View
                className={`no-more no-more--enhanced ${
                  customStyles.loadingState || ''
                }`}
              >
                <Text>没有更多数据了</Text>
              </View>
            )}
        </ScrollView>
      </View>
    </View>
  );
};

export default NewsComponent;
