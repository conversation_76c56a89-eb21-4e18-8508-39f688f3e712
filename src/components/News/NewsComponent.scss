/**
 * 新闻组件样式 - 基于NutUI的Tabs和VirtualList增强版
 * 支持粘性布局、虚拟列表、多种使用场景
 *
 * 样式覆写指南：
 * 1. 使用 customStyles props 传入自定义类名
 * 2. 可覆写的类名：
 *    - container: 主容器样式 (.news-component)
 *    - tabsContainer: Tabs容器样式 (.news-categories)
 *    - tabs: Tabs样式 (.news-tabs)
 *    - listContainer: VirtualList容器样式 (.news-list-container)
 *    - virtualList: VirtualList样式 (.news-virtual-list)
 *    - newsItem: 新闻项样式 (.news-item-wrapper)
 *    - loadingState: 加载状态样式 (.loading-more, .no-more)
 *
 * 例如：
 * <NewsComponent
 *   customStyles={{
 *     container: 'my-custom-news-container',
 *     tabs: 'my-custom-tabs',
 *     newsItem: 'my-custom-news-item'
 *   }}
 * />
 *
 * 这些自定义类名会与原有类名合并，允许你覆写或扩展样式。
 */
.news-component {
  width: 100%;
  background: var(--color-background);
  color: var(--color-text-primary);
  position: relative;

  // 增强版组件特殊样式
  &--enhanced {
    display: flex;
    flex-direction: column;
    height: 100%;

    &.news-component--sticky {
      .news-categories {
        &::after {
          opacity: 1;
          box-shadow: var(--shadow-sm);
        }
      }
    }
  }

  // NutUI Sticky容器样式
  .sticky-tabs-container {
    z-index: 100;
    background: var(--color-background);

    :global(.nut-sticky) {
      background: var(--color-background);
      border-bottom: 1px solid var(--color-border);
    }
  }

  // 新闻分类容器
  .news-categories {
    background: var(--color-background);
    position: relative;
    transition: all 0.3s ease;

    // 粘性状态样式
    &--sticky {
      box-shadow: var(--shadow-sm);
      border-bottom: 1px solid var(--color-border);
    }

    // 添加阴影效果
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: var(--color-border);
      opacity: 0.5;
      transition: all 0.3s ease;
    }
  }

  // NutUI Tabs 增强样式
  .news-tabs {
    &--enhanced {
      // 覆盖NutUI默认样式
      :global(.nut-tabs__titles) {
        background: transparent;
        border: none;
        padding: 0;
        display: flex;
        justify-content: flex-start;
        gap: 0.5rem;
      }

      :global(.nut-tabs__titles-item) {
        padding: 0.5rem 1rem;
        background: var(--color-surface);
        border: 1px solid var(--color-border);
        border-radius: 20px;
        color: var(--color-text-secondary);
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.2s ease-in-out;
        white-space: nowrap;
        min-width: auto;
        flex: none;

        &:hover {
          border-color: var(--color-primary);
          transform: translateY(-1px);
        }

        &.nut-tabs__titles-item--active {
          background: var(--color-primary);
          color: #fff;
          border-color: var(--color-primary);
          box-shadow: var(--shadow-sm);
        }
      }

      :global(.nut-tabs__line) {
        display: none; // 隐藏默认的下划线
      }

      :global(.nut-tabs__content) {
        display: none; // 隐藏TabPane内容，使用VirtualList替代
      }
    }
  }

  // 新闻列表容器 - 增强版
  .news-list-container {
    flex: 1;
    overflow: hidden;

    &--virtual {
      height: 100%;
      position: relative;
    }

    &--scroll {
      height: 100%;
      position: relative;
    }
  }

  // ScrollView 样式
  .news-scroll-view {
    width: 100%;
    height: 100%;
    background: transparent;
  }

  // VirtualList 样式（保留兼容性）
  .news-virtual-list {
    width: 100%;
    height: 100%;

    &--content {
      :global(.nut-virtuallist) {
        background: transparent;

        .nut-virtuallist-container {
          padding: 0;
        }
      }
    }

    &--skeleton {
      :global(.nut-virtuallist) {
        background: transparent;
      }
    }
  }

  // 虚拟列表中的新闻项容器
  .virtual-news-item {
    padding: 0.5rem 1rem;

    &:first-child {
      padding-top: 1rem;
    }

    &:last-child {
      padding-bottom: 1rem;
    }
  }

  // 新闻列表
  .news-list {
    padding: 0;
  }

  // 新闻项包装器（用于VirtualList）
  .news-item-wrapper {
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      opacity: 0.9;
    }
  }

  // 新闻项内容
  .news-item {
    padding: 1rem;
    background: var(--color-surface);
    border-radius: 12px;
    border: 1px solid var(--color-border);
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 100%
      );
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover {
      border-color: var(--color-primary);
      box-shadow: var(--shadow-md);

      &::before {
        opacity: 1;
      }
    }

    .news-content {
      .news-title {
        width: 16rem;
        font-size: 0.8rem;
        font-weight: 600;
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
        display: block;
        line-height: 1.4;
      }

      .news-summary {
        width: 16rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: wrap;
        display: -webkit-box;
        line-clamp: 2;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 0.55rem;
        color: var(--color-text-secondary);
        margin-bottom: 0.5rem;
        display: block;
        line-height: 1.5;
      }

      .news-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .news-source {
          font-size: 0.75rem;
          color: var(--color-primary);
          font-weight: 500;
        }

        .news-date {
          font-size: 0.75rem;
          color: var(--color-text-secondary);
        }
      }
    }

    .news-image {
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }

  // 无限滚动相关样式 - 增强版
  .load-more-sentinel {
    height: 1px;
    width: 100%;
    background: transparent;
    pointer-events: none;
  }

  .loading-more,
  .no-more,
  .view-more {
    padding: 1rem 0;
    text-align: center;
    color: var(--color-text-secondary);
    font-size: 0.8rem;

    &--enhanced {
      background: var(--color-surface);
      margin: 0.5rem 1rem;
      border-radius: 8px;
      border: 1px solid var(--color-border);
    }
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &::before {
      content: '';
      width: 1rem;
      height: 1rem;
      border: 2px solid var(--color-border);
      border-top-color: var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    &--enhanced {
      background: linear-gradient(
        135deg,
        var(--color-surface) 0%,
        var(--color-background) 100%
      );
    }
  }

  .no-more {
    opacity: 0.6;

    &--enhanced {
      font-style: italic;
    }
  }

  .view-more {
    padding: 1.5rem 0;

    &--enhanced {
      padding: 2rem 0;
      background: linear-gradient(
        135deg,
        var(--color-primary-light) 0%,
        var(--color-surface) 100%
      );
      border-color: var(--color-primary);
    }
  }

  // 错误状态样式
  &.news-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem 1rem;

    .error-message {
      color: var(--color-text-secondary);
      font-size: 0.85rem;
      text-align: center;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 响应式样式
@media (max-width: 640px) {
  .news-component {
    .news-list {
      .news-item {
        padding: 0.75rem;

        .news-content {
          .news-title {
            font-size: 0.9rem;
          }

          .news-summary {
            font-size: 0.8rem;
          }
        }
      }
    }
  }
}

// 小程序端特殊样式
.platform-weapp,
.platform-alipay,
.platform-swan,
.platform-tt,
.platform-qq,
.platform-jd {
  .news-component {
    .news-item {
      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }
    }
  }
}

// H5端特殊样式
.platform-h5 {
  .news-component {
    .news-item {
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }
    }
  }
}
