import React, { createContext, useContext, useMemo } from 'react';
import { useThemeStore } from '../stores/themeStore';
import { platform } from '../utils/platform';

interface ThemeContextValue {
  currentTheme: any;
  mode: string;
}

const ThemeContext = createContext<ThemeContextValue | null>(null);

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { currentTheme, mode } = useThemeStore();

  const contextValue = useMemo(
    () => ({
      currentTheme,
      mode,
    }),
    [currentTheme, mode]
  );

  // 在小程序环境中，直接返回子组件，不使用 Material-UI
  if (platform.isMiniProgram()) {
    return <>{children}</>;
  }

  // 在 H5 环境中，可以使用更复杂的主题系统
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// 导出 useTheme hook 供其他组件使用
export const useTheme = () => {
  const context = useContext(ThemeContext);
  const { currentTheme, mode } = useThemeStore();

  // 如果在小程序环境或没有 context，直接从 store 获取
  if (!context || platform.isMiniProgram()) {
    return { currentTheme, mode };
  }

  return context;
};

export default ThemeProvider;
