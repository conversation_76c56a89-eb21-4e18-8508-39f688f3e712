import { View } from '@tarojs/components';
import classNames from 'classnames';
import React from 'react';
import { useResponsive } from '../../hooks/useResponsive';

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'full';
  padding?: boolean;
  center?: boolean;
  fluid?: boolean;
}

const Container: React.FC<ContainerProps> = ({
  children,
  className,
  maxWidth = 'xl',
  padding = true,
  center = true,
  fluid = false,
}) => {
  const { isMobile, isTablet } = useResponsive();

  const containerClasses = classNames(
    'w-full',
    {
      // 最大宽度设置
      'max-w-xs': maxWidth === 'xs' && !fluid,
      'max-w-sm': maxWidth === 'sm' && !fluid,
      'max-w-md': maxWidth === 'md' && !fluid,
      'max-w-lg': maxWidth === 'lg' && !fluid,
      'max-w-xl': maxWidth === 'xl' && !fluid,
      'max-w-2xl': maxWidth === 'xxl' && !fluid,

      // 居中
      'mx-auto': center,

      // 内边距
      'px-4': padding && isMobile,
      'px-6': padding && isTablet,
      'px-8': padding && !isMobile && !isTablet,
    },
    className
  );

  return <View className={containerClasses}>{children}</View>;
};

export default Container;
