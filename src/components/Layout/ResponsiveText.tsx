import { Text } from '@tarojs/components';
import classNames from 'classnames';
import React from 'react';
import { useResponsiveValue } from '../../hooks/useResponsive';

interface ResponsiveTextProps {
  children: React.ReactNode;
  className?: string;
  variant?:
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'h5'
    | 'h6'
    | 'body1'
    | 'body2'
    | 'caption'
    | 'overline';
  color?:
    | 'primary'
    | 'secondary'
    | 'textPrimary'
    | 'textSecondary'
    | 'error'
    | 'warning'
    | 'success'
    | 'info';
  align?: 'left' | 'center' | 'right' | 'justify';
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
  size?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    xxl?: string;
  };
  lineHeight?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    xxl?: string;
  };
  noWrap?: boolean;
  ellipsis?: boolean;
}

const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  className,
  variant = 'body1',
  color = 'textPrimary',
  align = 'left',
  weight = 'normal',
  size,
  lineHeight,
  noWrap = false,
  ellipsis = false,
}) => {
  // 获取响应式字体大小
  const responsiveFontSize = useResponsiveValue(
    size || {
      xs: getVariantSize(variant, 'xs'),
      sm: getVariantSize(variant, 'sm'),
      md: getVariantSize(variant, 'md'),
      lg: getVariantSize(variant, 'lg'),
      xl: getVariantSize(variant, 'xl'),
      xxl: getVariantSize(variant, 'xxl'),
    }
  );

  // 获取响应式行高
  const responsiveLineHeight = useResponsiveValue(
    lineHeight || {
      xs: getVariantLineHeight(variant),
      sm: getVariantLineHeight(variant),
      md: getVariantLineHeight(variant),
      lg: getVariantLineHeight(variant),
      xl: getVariantLineHeight(variant),
      xxl: getVariantLineHeight(variant),
    }
  );

  const textClasses = classNames(
    // 基础样式
    'block',

    // 颜色
    {
      'text-blue-600': color === 'primary',
      'text-pink-600': color === 'secondary',
      'text-gray-900': color === 'textPrimary',
      'text-gray-600': color === 'textSecondary',
      'text-red-600': color === 'error',
      'text-orange-600': color === 'warning',
      'text-green-600': color === 'success',
      'text-blue-500': color === 'info',
    },

    // 对齐方式
    {
      'text-left': align === 'left',
      'text-center': align === 'center',
      'text-right': align === 'right',
      'text-justify': align === 'justify',
    },

    // 字重
    {
      'font-light': weight === 'light',
      'font-normal': weight === 'normal',
      'font-medium': weight === 'medium',
      'font-semibold': weight === 'semibold',
      'font-bold': weight === 'bold',
    },

    // 文本处理
    {
      'whitespace-nowrap': noWrap,
      truncate: ellipsis,
    },

    className
  );

  const textStyle = {
    fontSize: responsiveFontSize,
    lineHeight: responsiveLineHeight,
  };

  return (
    <Text className={textClasses} style={textStyle}>
      {children}
    </Text>
  );
};

// 获取变体对应的字体大小
function getVariantSize(variant: string, breakpoint: string): string {
  const sizeMap = {
    h1: {
      xs: '2rem',
      sm: '2.25rem',
      md: '2.5rem',
      lg: '3rem',
      xl: '3.5rem',
      xxl: '4rem',
    },
    h2: {
      xs: '1.75rem',
      sm: '2rem',
      md: '2.25rem',
      lg: '2.5rem',
      xl: '3rem',
      xxl: '3.5rem',
    },
    h3: {
      xs: '1.5rem',
      sm: '1.75rem',
      md: '2rem',
      lg: '2.25rem',
      xl: '2.5rem',
      xxl: '3rem',
    },
    h4: {
      xs: '1.25rem',
      sm: '1.5rem',
      md: '1.75rem',
      lg: '2rem',
      xl: '2.25rem',
      xxl: '2.5rem',
    },
    h5: {
      xs: '1.125rem',
      sm: '1.25rem',
      md: '1.5rem',
      lg: '1.75rem',
      xl: '2rem',
      xxl: '2.25rem',
    },
    h6: {
      xs: '1rem',
      sm: '1.125rem',
      md: '1.25rem',
      lg: '1.5rem',
      xl: '1.75rem',
      xxl: '2rem',
    },
    body1: {
      xs: '0.875rem',
      sm: '1rem',
      md: '1rem',
      lg: '1.125rem',
      xl: '1.125rem',
      xxl: '1.25rem',
    },
    body2: {
      xs: '0.75rem',
      sm: '0.875rem',
      md: '0.875rem',
      lg: '1rem',
      xl: '1rem',
      xxl: '1.125rem',
    },
    caption: {
      xs: '0.625rem',
      sm: '0.75rem',
      md: '0.75rem',
      lg: '0.875rem',
      xl: '0.875rem',
      xxl: '1rem',
    },
    overline: {
      xs: '0.625rem',
      sm: '0.75rem',
      md: '0.75rem',
      lg: '0.875rem',
      xl: '0.875rem',
      xxl: '1rem',
    },
  };

  return sizeMap[variant]?.[breakpoint] || '1rem';
}

// 获取变体对应的行高
function getVariantLineHeight(variant: string): string {
  const lineHeightMap = {
    h1: '1.2',
    h2: '1.2',
    h3: '1.3',
    h4: '1.3',
    h5: '1.4',
    h6: '1.4',
    body1: '1.5',
    body2: '1.5',
    caption: '1.4',
    overline: '1.4',
  };

  return lineHeightMap[variant] || '1.5';
}

export default ResponsiveText;
