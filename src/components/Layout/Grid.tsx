import { View } from '@tarojs/components';
import classNames from 'classnames';
import React from 'react';
import { generateResponsiveClasses } from '../../utils/responsive';

interface GridProps {
  children: React.ReactNode;
  className?: string;
  container?: boolean;
  item?: boolean;
  spacing?: number;
  xs?: number | 'auto';
  sm?: number | 'auto';
  md?: number | 'auto';
  lg?: number | 'auto';
  xl?: number | 'auto';
  xxl?: number | 'auto';
  direction?: 'row' | 'column';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  align?: 'start' | 'center' | 'end' | 'stretch';
  wrap?: boolean;
}

const Grid: React.FC<GridProps> = ({
  children,
  className,
  container = false,
  item = false,
  spacing = 0,
  xs,
  sm,
  md,
  lg,
  xl,
  xxl,
  direction = 'row',
  justify = 'start',
  align = 'stretch',
  wrap = true,
}) => {
  const containerClasses = classNames(
    {
      // 容器样式
      flex: container,
      'flex-row': container && direction === 'row',
      'flex-col': container && direction === 'column',
      'flex-wrap': container && wrap,
      'flex-nowrap': container && !wrap,

      // 对齐方式
      'justify-start': container && justify === 'start',
      'justify-center': container && justify === 'center',
      'justify-end': container && justify === 'end',
      'justify-between': container && justify === 'between',
      'justify-around': container && justify === 'around',
      'justify-evenly': container && justify === 'evenly',

      'items-start': container && align === 'start',
      'items-center': container && align === 'center',
      'items-end': container && align === 'end',
      'items-stretch': container && align === 'stretch',

      // 间距
      [`gap-${spacing}`]: container && spacing > 0,

      // 项目样式
      'flex-1': item && xs === 'auto',
      'flex-none': item && xs !== 'auto',
    },
    className
  );

  // 生成响应式宽度类名
  const responsiveClasses = item
    ? generateResponsiveClasses('w', {
        xs: xs === 'auto' ? 'auto' : xs ? `${(xs / 12) * 100}%` : undefined,
        sm: sm === 'auto' ? 'auto' : sm ? `${(sm / 12) * 100}%` : undefined,
        md: md === 'auto' ? 'auto' : md ? `${(md / 12) * 100}%` : undefined,
        lg: lg === 'auto' ? 'auto' : lg ? `${(lg / 12) * 100}%` : undefined,
        xl: xl === 'auto' ? 'auto' : xl ? `${(xl / 12) * 100}%` : undefined,
        xxl: xxl === 'auto' ? 'auto' : xxl ? `${(xxl / 12) * 100}%` : undefined,
      })
    : '';

  const finalClasses = classNames(containerClasses, responsiveClasses);

  return <View className={finalClasses}>{children}</View>;
};

export default Grid;
