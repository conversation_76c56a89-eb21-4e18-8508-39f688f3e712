import { Swiper, SwiperItem, View } from '@tarojs/components';
import React from 'react';
import './index.scss';

interface CarouselItem {
  id: string;
  title?: string;
  description?: string;
  imageUrl?: string;
  link?: string;
}

interface CarouselProps {
  items?: CarouselItem[];
  height?: string;
  autoplay?: boolean;
  interval?: number;
  circular?: boolean;
  indicatorDots?: boolean;
  className?: string;
}

const Carousel: React.FC<CarouselProps> = ({
  items = [],
  height = '200px',
  autoplay = true,
  interval = 3000,
  circular = true,
  indicatorDots = true,
  className = '',
}) => {
  // 如果没有数据，显示占位符
  const displayItems = items.length > 0 ? items : [
    { id: '1', title: '轮播图 1', description: '预留位置' },
    { id: '2', title: '轮播图 2', description: '预留位置' },
    { id: '3', title: '轮播图 3', description: '预留位置' },
  ];

  return (
    <View className={`carousel ${className}`}>
      <Swiper
        className='carousel-swiper'
        style={{ height }}
        autoplay={autoplay}
        interval={interval}
        circular={circular}
        indicatorDots={indicatorDots}
        indicatorColor='rgba(255, 255, 255, 0.3)'
        indicatorActiveColor='#ffffff'
      >
        {displayItems.map((item) => (
          <SwiperItem key={item.id} className='carousel-item'>
            <View className='carousel-content'>
              {item.imageUrl ? (
                <View 
                  className='carousel-image'
                  style={{ backgroundImage: `url(${item.imageUrl})` }}
                />
              ) : (
                <View className='carousel-placeholder'>
                  <View className='placeholder-content'>
                    <View className='placeholder-title'>{item.title}</View>
                    <View className='placeholder-description'>{item.description}</View>
                  </View>
                </View>
              )}
              {(item.title || item.description) && (
                <View className='carousel-overlay'>
                  {item.title && (
                    <View className='carousel-title'>{item.title}</View>
                  )}
                  {item.description && (
                    <View className='carousel-description'>{item.description}</View>
                  )}
                </View>
              )}
            </View>
          </SwiperItem>
        ))}
      </Swiper>
    </View>
  );
};

export default Carousel;
