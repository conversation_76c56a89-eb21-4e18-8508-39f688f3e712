// 轮播组件样式
.carousel {
  width: 100%;
  margin-bottom: 1.5rem;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  // 暗色主题适配
  [data-theme='dark'] & {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  }
}

.carousel-swiper {
  width: 100%;
  border-radius: 0.75rem;
}

.carousel-item {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-content {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 轮播图片
.carousel-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

// 占位符样式
.carousel-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  // 暗色主题适配
  [data-theme='dark'] & {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  }

  .placeholder-content {
    text-align: center;
    color: #ffffff;
    z-index: 2;
  }

  .placeholder-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .placeholder-description {
    font-size: 0.875rem;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  // 添加装饰性元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
  }
}

// 文字覆盖层
.carousel-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1.5rem 1rem 1rem;
  color: #ffffff;
  z-index: 3;
}

.carousel-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.carousel-description {
  font-size: 0.875rem;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

// 响应式设计
@media (max-width: 640px) {
  .carousel {
    margin-bottom: 1rem;
    border-radius: 0.5rem;
  }

  .carousel-swiper {
    border-radius: 0.5rem;
  }

  .placeholder-title {
    font-size: 1.125rem;
  }

  .placeholder-description {
    font-size: 0.8rem;
  }

  .carousel-overlay {
    padding: 1rem 0.75rem 0.75rem;
  }

  .carousel-title {
    font-size: 1rem;
  }

  .carousel-description {
    font-size: 0.8rem;
  }
}
