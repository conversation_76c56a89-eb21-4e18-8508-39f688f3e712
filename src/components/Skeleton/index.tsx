import React from 'react';
import { View } from '@tarojs/components';
import './index.scss';

interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: string | number;
  className?: string;
  animated?: boolean;
}

// 基础骨架屏组件
export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '20px',
  borderRadius = '4px',
  className = '',
  animated = true,
}) => {
  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
    borderRadius: typeof borderRadius === 'number' ? `${borderRadius}px` : borderRadius,
  };

  return (
    <View
      className={`skeleton ${animated ? 'skeleton--animated' : ''} ${className}`}
      style={style}
    />
  );
};

// 加密货币卡片骨架屏
export const CryptoCardSkeleton: React.FC<{ count?: number }> = ({ count = 1 }) => {
  return (
    <View className="crypto-skeleton-container">
      {Array.from({ length: count }).map((_, index) => (
        <View key={index} className="crypto-skeleton-card">
          <View className="crypto-skeleton-header">
            <Skeleton width={40} height={40} borderRadius="50%" />
            <View className="crypto-skeleton-info">
              <Skeleton width={80} height={16} />
              <Skeleton width={60} height={12} />
            </View>
            <View className="crypto-skeleton-price">
              <Skeleton width={100} height={18} />
              <Skeleton width={80} height={14} />
            </View>
          </View>
          <View className="crypto-skeleton-chart">
            <Skeleton width="100%" height={60} borderRadius={8} />
          </View>
        </View>
      ))}
    </View>
  );
};

// 新闻卡片骨架屏
export const NewsCardSkeleton: React.FC<{ count?: number }> = ({ count = 1 }) => {
  return (
    <View className="news-skeleton-container">
      {Array.from({ length: count }).map((_, index) => (
        <View key={index} className="news-skeleton-card">
          <View className="news-skeleton-image">
            <Skeleton width="100%" height={120} borderRadius={8} />
          </View>
          <View className="news-skeleton-content">
            <Skeleton width="100%" height={20} />
            <Skeleton width="90%" height={16} />
            <Skeleton width="70%" height={16} />
            <View className="news-skeleton-meta">
              <Skeleton width={80} height={12} />
              <Skeleton width={100} height={12} />
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

// 趋势话题骨架屏
export const TrendingTopicSkeleton: React.FC<{ count?: number }> = ({ count = 1 }) => {
  return (
    <View className="trending-skeleton-container">
      {Array.from({ length: count }).map((_, index) => (
        <View key={index} className="trending-skeleton-item">
          <View className="trending-skeleton-content">
            <Skeleton width={120} height={16} />
            <Skeleton width={80} height={14} />
          </View>
          <View className="trending-skeleton-badge">
            <Skeleton width={60} height={24} borderRadius={12} />
          </View>
        </View>
      ))}
    </View>
  );
};

// 列表骨架屏
export const ListSkeleton: React.FC<{
  count?: number;
  itemHeight?: number;
  showAvatar?: boolean;
  showImage?: boolean;
}> = ({ count = 5, itemHeight = 80, showAvatar = false, showImage = false }) => {
  return (
    <View className="list-skeleton-container">
      {Array.from({ length: count }).map((_, index) => (
        <View key={index} className="list-skeleton-item" style={{ height: `${itemHeight}px` }}>
          {showAvatar && (
            <Skeleton width={40} height={40} borderRadius="50%" className="list-skeleton-avatar" />
          )}
          {showImage && (
            <Skeleton width={60} height={60} borderRadius={8} className="list-skeleton-image" />
          )}
          <View className="list-skeleton-content">
            <Skeleton width="80%" height={16} />
            <Skeleton width="60%" height={14} />
            {itemHeight > 60 && <Skeleton width="40%" height={12} />}
          </View>
        </View>
      ))}
    </View>
  );
};

// 表格骨架屏
export const TableSkeleton: React.FC<{
  rows?: number;
  columns?: number;
  showHeader?: boolean;
}> = ({ rows = 5, columns = 4, showHeader = true }) => {
  return (
    <View className="table-skeleton-container">
      {showHeader && (
        <View className="table-skeleton-header">
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton key={index} width="80%" height={16} />
          ))}
        </View>
      )}
      <View className="table-skeleton-body">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <View key={rowIndex} className="table-skeleton-row">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton key={colIndex} width="70%" height={14} />
            ))}
          </View>
        ))}
      </View>
    </View>
  );
};

// 卡片骨架屏
export const CardSkeleton: React.FC<{
  hasImage?: boolean;
  hasAvatar?: boolean;
  lines?: number;
}> = ({ hasImage = false, hasAvatar = false, lines = 3 }) => {
  return (
    <View className="card-skeleton">
      {hasImage && (
        <Skeleton width="100%" height={200} borderRadius="8px 8px 0 0" />
      )}
      <View className="card-skeleton-content">
        {hasAvatar && (
          <View className="card-skeleton-header">
            <Skeleton width={40} height={40} borderRadius="50%" />
            <View className="card-skeleton-header-content">
              <Skeleton width={120} height={16} />
              <Skeleton width={80} height={12} />
            </View>
          </View>
        )}
        <View className="card-skeleton-body">
          {Array.from({ length: lines }).map((_, index) => (
            <Skeleton
              key={index}
              width={index === lines - 1 ? '60%' : '100%'}
              height={14}
            />
          ))}
        </View>
      </View>
    </View>
  );
};

// 页面骨架屏
export const PageSkeleton: React.FC<{
  showHeader?: boolean;
  showTabs?: boolean;
  contentType?: 'list' | 'cards' | 'table';
}> = ({ showHeader = true, showTabs = false, contentType = 'list' }) => {
  return (
    <View className="page-skeleton">
      {showHeader && (
        <View className="page-skeleton-header">
          <Skeleton width={150} height={24} />
          <Skeleton width={80} height={32} borderRadius={16} />
        </View>
      )}
      
      {showTabs && (
        <View className="page-skeleton-tabs">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} width={60} height={32} borderRadius={16} />
          ))}
        </View>
      )}
      
      <View className="page-skeleton-content">
        {contentType === 'list' && <ListSkeleton count={8} />}
        {contentType === 'cards' && (
          <View className="page-skeleton-cards">
            {Array.from({ length: 6 }).map((_, index) => (
              <CardSkeleton key={index} hasImage lines={2} />
            ))}
          </View>
        )}
        {contentType === 'table' && <TableSkeleton rows={10} />}
      </View>
    </View>
  );
};

export default Skeleton;