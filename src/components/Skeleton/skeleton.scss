// 骨架屏基础样式
.skeleton-base {
  display: block;
  background-color: #f0f0f0;
  border-radius: 4px;

  &.skeleton-dark {
    background-color: #2a2a2a;
  }
}

// 骨架屏变体
.skeleton-text {
  height: 1rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.skeleton-rectangular {
  border-radius: 4px;
}

.skeleton-circular {
  border-radius: 50%;
}

.skeleton-rounded {
  border-radius: 8px;
}

// 骨架屏动画
.skeleton-pulse {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton-wave {
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: skeleton-wave 1.5s ease-in-out infinite;
  }

  &.skeleton-dark::after {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }
}

// 动画定义
@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

@keyframes skeleton-wave {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 骨架屏组合组件样式
.skeleton-text-container {
  .skeleton-text {
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.skeleton-card {
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background: var(--color-surface);

  .skeleton-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;

    .skeleton-card-header-content {
      margin-left: 0.75rem;
      flex: 1;

      .skeleton-base {
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .skeleton-card-title {
    margin-bottom: 1rem;
  }

  .skeleton-card-content {
    .skeleton-text {
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.skeleton-list-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid var(--color-border);

  &:last-child {
    border-bottom: none;
  }

  .skeleton-list-item-content {
    margin-left: 0.75rem;
    flex: 1;

    .skeleton-base {
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.skeleton-image-container {
  position: relative;

  .skeleton-image-wrapper {
    position: relative;
    width: 100%;

    .skeleton-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}

.skeleton-table {
  width: 100%;
  border-collapse: collapse;

  .skeleton-table-row {
    display: flex;
    border-bottom: 1px solid var(--color-border);

    &:last-child {
      border-bottom: none;
    }

    .skeleton-table-cell {
      flex: 1;
      padding: 0.75rem;
      margin: 0.25rem;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

// 响应式骨架屏
@media (max-width: 640px) {
  .skeleton-card {
    padding: 0.75rem;

    .skeleton-card-header {
      margin-bottom: 0.75rem;

      .skeleton-card-header-content {
        margin-left: 0.5rem;
      }
    }

    .skeleton-card-title {
      margin-bottom: 0.75rem;
    }
  }

  .skeleton-list-item {
    padding: 0.5rem;

    .skeleton-list-item-content {
      margin-left: 0.5rem;
    }
  }

  .skeleton-table {
    .skeleton-table-row {
      .skeleton-table-cell {
        padding: 0.5rem;
        margin: 0.125rem;
      }
    }
  }
}

// 小程序端适配
.platform-weapp,
.platform-alipay,
.platform-swan,
.platform-tt,
.platform-qq,
.platform-jd {
  .skeleton-base {
    background-color: #f0f0f0;

    &.skeleton-dark {
      background-color: #2a2a2a;
    }
  }

  .skeleton-wave::after {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
  }

  .skeleton-dark.skeleton-wave::after {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }
}

// H5端增强样式
.platform-h5 {
  .skeleton-card {
    transition: box-shadow 0.2s ease-in-out;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .skeleton-list-item {
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: var(--color-surface);
    }
  }
}
