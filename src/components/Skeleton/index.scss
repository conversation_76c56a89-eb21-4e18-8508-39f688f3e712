// 骨架屏样式
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  
  &--animated {
    animation: skeleton-loading 1.5s infinite;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 暗色主题下的骨架屏
.dark .skeleton {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200% 100%;
}

// 加密货币卡片骨架屏
.crypto-skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.crypto-skeleton-card {
  padding: 16px;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #1a1a1a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.crypto-skeleton-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.crypto-skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.crypto-skeleton-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.crypto-skeleton-chart {
  margin-top: 12px;
}

// 新闻卡片骨架屏
.news-skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.news-skeleton-card {
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .dark & {
    background: #1a1a1a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.news-skeleton-image {
  width: 100%;
  padding: 0;
}

.news-skeleton-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.news-skeleton-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

// 趋势话题骨架屏
.trending-skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trending-skeleton-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #1a1a1a;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  }
}

.trending-skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trending-skeleton-badge {
  margin-left: 12px;
}

// 列表骨架屏
.list-skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.list-skeleton-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #1a1a1a;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  }
}

.list-skeleton-avatar {
  flex-shrink: 0;
}

.list-skeleton-image {
  flex-shrink: 0;
}

.list-skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// 表格骨架屏
.table-skeleton-container {
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .dark & {
    background: #1a1a1a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.table-skeleton-header {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  
  .dark & {
    border-bottom-color: #3a3a3a;
  }
}

.table-skeleton-body {
  display: flex;
  flex-direction: column;
}

.table-skeleton-row {
  display: flex;
  gap: 16px;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .dark & {
    border-bottom-color: #2a2a2a;
  }
}

// 卡片骨架屏
.card-skeleton {
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .dark & {
    background: #1a1a1a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.card-skeleton-content {
  padding: 16px;
}

.card-skeleton-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.card-skeleton-header-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-skeleton-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 页面骨架屏
.page-skeleton {
  padding: 16px;
}

.page-skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-skeleton-tabs {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.page-skeleton-content {
  margin-top: 16px;
}

.page-skeleton-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .crypto-skeleton-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .crypto-skeleton-price {
    align-items: flex-start;
  }
  
  .news-skeleton-content {
    padding: 12px;
  }
  
  .page-skeleton-cards {
    grid-template-columns: 1fr;
  }
  
  .table-skeleton-header,
  .table-skeleton-row {
    padding: 8px 12px;
    gap: 8px;
  }
}

// 小程序特殊样式
@media (max-width: 480px) {
  .crypto-skeleton-card,
  .news-skeleton-card,
  .card-skeleton {
    margin: 0 8px;
  }
  
  .list-skeleton-item {
    padding: 8px 12px;
  }
  
  .page-skeleton {
    padding: 12px;
  }
}