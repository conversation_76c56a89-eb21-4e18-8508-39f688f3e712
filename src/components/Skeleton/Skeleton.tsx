import { View } from '@tarojs/components';
import classNames from 'classnames';
import React from 'react';
import { useThemeStore } from '../../stores/themeStore';

interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  variant?: 'text' | 'rectangular' | 'circular' | 'rounded';
  animation?: 'pulse' | 'wave' | 'none';
  loading?: boolean;
  children?: React.ReactNode;
}

const Skeleton: React.FC<SkeletonProps> = ({
  className,
  width = '100%',
  height = '1rem',
  variant = 'text',
  animation = 'pulse',
  loading = true,
  children,
}) => {
  const { mode } = useThemeStore();

  if (!loading && children) {
    return <>{children}</>;
  }

  const skeletonClasses = classNames(
    'skeleton-base',
    {
      // 变体样式
      'skeleton-text': variant === 'text',
      'skeleton-rectangular': variant === 'rectangular',
      'skeleton-circular': variant === 'circular',
      'skeleton-rounded': variant === 'rounded',

      // 动画样式
      'skeleton-pulse': animation === 'pulse',
      'skeleton-wave': animation === 'wave',

      // 主题样式
      'skeleton-light': mode === 'light',
      'skeleton-dark': mode === 'dark',
    },
    className
  );

  const skeletonStyle = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  return <View className={skeletonClasses} style={skeletonStyle} />;
};

// 文本骨架屏
export const SkeletonText: React.FC<
  Omit<SkeletonProps, 'variant'> & { lines?: number }
> = ({ lines = 1, ...props }) => {
  if (lines === 1) {
    return <Skeleton {...props} variant='text' />;
  }

  return (
    <View className='skeleton-text-container'>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          {...props}
          variant='text'
          className={classNames(props.className, { 'mb-2': index < lines - 1 })}
          width={index === lines - 1 ? '60%' : '100%'}
        />
      ))}
    </View>
  );
};

// 头像骨架屏
export const SkeletonAvatar: React.FC<
  Omit<SkeletonProps, 'variant'> & { size?: number }
> = ({ size = 40, ...props }) => (
  <Skeleton {...props} variant='circular' width={size} height={size} />
);

// 卡片骨架屏
export const SkeletonCard: React.FC<{
  className?: string;
  loading?: boolean;
  children?: React.ReactNode;
  showAvatar?: boolean;
  showTitle?: boolean;
  showContent?: boolean;
  contentLines?: number;
}> = ({
  className,
  loading = true,
  children,
  showAvatar = true,
  showTitle = true,
  showContent = true,
  contentLines = 3,
}) => {
  if (!loading && children) {
    return <>{children}</>;
  }

  return (
    <View className={classNames('skeleton-card', className)}>
      {showAvatar && (
        <View className='skeleton-card-header'>
          <SkeletonAvatar size={48} />
          <View className='skeleton-card-header-content'>
            <Skeleton width='60%' height='1rem' />
            <Skeleton width='40%' height='0.875rem' />
          </View>
        </View>
      )}

      {showTitle && (
        <Skeleton width='80%' height='1.5rem' className='skeleton-card-title' />
      )}

      {showContent && (
        <View className='skeleton-card-content'>
          <SkeletonText lines={contentLines} />
        </View>
      )}
    </View>
  );
};

// 列表项骨架屏
export const SkeletonListItem: React.FC<{
  className?: string;
  loading?: boolean;
  children?: React.ReactNode;
  showAvatar?: boolean;
  showSecondary?: boolean;
}> = ({
  className,
  loading = true,
  children,
  showAvatar = true,
  showSecondary = true,
}) => {
  if (!loading && children) {
    return <>{children}</>;
  }

  return (
    <View className={classNames('skeleton-list-item', className)}>
      {showAvatar && <SkeletonAvatar size={40} />}
      <View className='skeleton-list-item-content'>
        <Skeleton width='70%' height='1rem' />
        {showSecondary && <Skeleton width='50%' height='0.875rem' />}
      </View>
    </View>
  );
};

// 图片骨架屏
export const SkeletonImage: React.FC<
  Omit<SkeletonProps, 'variant'> & {
    aspectRatio?: number;
  }
> = ({ aspectRatio = 16 / 9, width = '100%', ...props }) => {
  const height = aspectRatio ? `${100 / aspectRatio}%` : props.height;

  return (
    <View className='skeleton-image-container' style={{ width }}>
      <View
        className='skeleton-image-wrapper'
        style={{ paddingBottom: height }}
      >
        <Skeleton
          {...props}
          variant='rectangular'
          width='100%'
          height='100%'
          className='skeleton-image'
        />
      </View>
    </View>
  );
};

// 表格骨架屏
export const SkeletonTable: React.FC<{
  className?: string;
  loading?: boolean;
  children?: React.ReactNode;
  rows?: number;
  columns?: number;
}> = ({ className, loading = true, children, rows = 5, columns = 4 }) => {
  if (!loading && children) {
    return <>{children}</>;
  }

  return (
    <View className={classNames('skeleton-table', className)}>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <View key={rowIndex} className='skeleton-table-row'>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton
              key={colIndex}
              width='100%'
              height='2rem'
              className='skeleton-table-cell'
            />
          ))}
        </View>
      ))}
    </View>
  );
};

export default Skeleton;
