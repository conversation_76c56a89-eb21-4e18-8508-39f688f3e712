import { ArrowLeft } from '@nutui/icons-react-taro';
import { Button } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import React, { ReactNode } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { navigation, platform } from '../../utils/platform';
import './NavigationBar.scss';

interface NavigationBarProps {
  title?: string;
  showBack?: boolean;
  showThemeToggle?: boolean;
  rightSlot?: ReactNode;
  onBackClick?: () => void;
  onRightClick?: () => void;
  className?: string;
  /** 是否在微信小程序中强制显示，默认false */
  forceShowInWeapp?: boolean;
}

const NavigationBar: React.FC<NavigationBarProps> = ({
  title = '',
  showBack = true,
  showThemeToggle = false,
  rightSlot,
  onBackClick,
  onRightClick,
  className = '',
  forceShowInWeapp = false,
}) => {
  const { mode, toggleTheme, currentTheme } = useThemeStore();

  // 在微信小程序中不显示NavigationBar（除非强制显示）
  if (platform.isWeapp() && !forceShowInWeapp) {
    return null;
  }

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      navigation.navigateBack();
    }
  };

  const handleThemeToggle = () => {
    if (onRightClick) {
      onRightClick();
    } else {
      toggleTheme();
    }
  };

  return (
    <View
      className={`navigation-bar ${className}`}
      style={{
        backgroundColor: currentTheme.surface,
        borderBottomColor: currentTheme.border,
      }}
    >
      {/* 左侧返回按钮 */}
      <View className='nav-left'>
        {showBack && (
          <ArrowLeft
            className='nav-back-btn'
            size='small'
            onClick={handleBackClick}
            style={{
              color: currentTheme.text.primary,
            }}
          />
        )}
      </View>

      {/* 中间标题 */}
      <View className='nav-center'>
        <Text
          className='nav-title'
          style={{
            color: currentTheme.text.primary,
          }}
        >
          {title}
        </Text>
      </View>

      {/* 右侧插槽 */}
      <View className='nav-right'>
        {rightSlot ? (
          <View onClick={onRightClick}>{rightSlot}</View>
        ) : showThemeToggle ? (
          <Button
            className='nav-theme-btn'
            size='small'
            fill='none'
            onClick={handleThemeToggle}
            style={{
              color: currentTheme.text.primary,
            }}
          >
            {mode === 'dark' ? '🌞' : mode === 'light' ? '🌙' : '🔄'}
          </Button>
        ) : null}
      </View>
    </View>
  );
};

export default NavigationBar;
