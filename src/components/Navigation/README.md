# NavigationBar 导航栏组件

一个通用的导航栏组件，支持主题切换、自定义插槽和响应式设计。

## 功能特性

- ✅ 左中右布局结构
- ✅ 返回按钮（可选）
- ✅ 页面标题显示
- ✅ 主题切换按钮（可选）
- ✅ 右侧自定义插槽
- ✅ 主题色联动
- ✅ 响应式设计
- ✅ 跨平台兼容（H5/小程序）

## 基本用法

```tsx
import { NavigationBar } from '../../components/Navigation';

// 基本用法
<NavigationBar title='页面标题' showBack={true} showThemeToggle={true} />;
```

## API

### Props

| 属性             | 类型       | 默认值 | 说明                       |
| ---------------- | ---------- | ------ | -------------------------- |
| title            | string     | ''     | 页面标题                   |
| showBack         | boolean    | true   | 是否显示返回按钮           |
| showThemeToggle  | boolean    | true   | 是否显示主题切换按钮       |
| rightSlot        | ReactNode  | -      | 右侧自定义插槽内容         |
| onBackClick      | () => void | -      | 返回按钮点击回调           |
| onRightClick     | () => void | -      | 右侧区域点击回调           |
| className        | string     | ''     | 自定义样式类名             |
| forceShowInWeapp | boolean    | false  | 是否在微信小程序中强制显示 |

### 使用示例

#### 1. 首页导航栏（无返回按钮）

```tsx
<NavigationBar title='加密货币资讯' showBack={false} showThemeToggle={true} />
```

#### 2. 普通页面导航栏

```tsx
<NavigationBar title='📰 加密货币资讯' showBack={true} showThemeToggle={true} />
```

#### 3. 自定义右侧插槽

```tsx
const rightSlot = (
  <View className='custom-actions'>
    <Button size='small' fill='none' onClick={handleSearch}>
      🔍
    </Button>
    <Button size='small' fill='none' onClick={handleFilter}>
      📊
    </Button>
  </View>
);

<NavigationBar
  title='💰 加密货币'
  showBack={true}
  showThemeToggle={false}
  rightSlot={rightSlot}
  onRightClick={handleCustomAction}
/>;
```

#### 4. 自定义返回逻辑

```tsx
const handleCustomBack = () => {
  // 自定义返回逻辑
  if (hasUnsavedChanges) {
    showConfirmDialog();
  } else {
    navigation.navigateBack();
  }
};

<NavigationBar
  title='编辑页面'
  showBack={true}
  onBackClick={handleCustomBack}
/>;
```

## 主题支持

导航栏会自动根据当前主题模式调整颜色：

- **背景色**: `currentTheme.surface`
- **边框色**: `currentTheme.border`
- **文字色**: `currentTheme.text.primary`

主题切换按钮显示逻辑：

- 🌙 (浅色模式)
- 🌞 (深色模式)
- 🔄 (自动模式)

## 样式定制

可以通过 `className` 属性添加自定义样式：

```tsx
<NavigationBar title='自定义样式' className='custom-nav' />
```

```scss
.custom-nav {
  // 自定义样式
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .nav-title {
    font-weight: 700;
  }
}
```

## 平台适配

### 自动平台判断

组件会自动检测当前运行环境：

- **微信小程序**: 默认不显示自定义导航栏，使用小程序原生导航栏
- **其他平台**: 正常显示自定义导航栏

### 强制显示

如果需要在微信小程序中显示自定义导航栏，可以使用 `forceShowInWeapp` 属性：

```tsx
// 在微信小程序中强制显示自定义导航栏
<NavigationBar title='特殊页面' forceShowInWeapp={true} />
```

### 平台检测原理

组件使用 `platform.isWeapp()` 方法检测是否为微信小程序环境：

```tsx
// 在微信小程序中不显示NavigationBar（除非强制显示）
if (platform.isWeapp() && !forceShowInWeapp) {
  return null;
}
```

这样做的好处：

1. 避免与小程序原生导航栏冲突
2. 保持各平台的原生体验
3. 减少小程序包体积
4. 提高小程序性能

## 注意事项

1. **平台适配**: 在微信小程序中，组件默认不渲染以避免与原生导航栏冲突
2. 当使用 `rightSlot` 时，`showThemeToggle` 会被忽略
3. 如果不提供 `onBackClick`，默认调用 `navigation.navigateBack()`
4. 导航栏使用 `position: sticky` 定位，会固定在页面顶部
5. 建议在页面组件的最外层使用，确保正确的层级关系
6. 如需在微信小程序中使用自定义导航栏，请设置 `forceShowInWeapp={true}`
