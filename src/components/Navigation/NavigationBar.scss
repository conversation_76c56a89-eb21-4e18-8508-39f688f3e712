.navigation-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 66px;
  padding: 0 32px;
  border-bottom: 1px solid;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;

  .nav-left {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .nav-back-btn {
      padding: 8px 16px;
      border: none;
      background: transparent;
      font-size: 28px;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.7;
      }

      &:active {
        opacity: 0.5;
      }
    }
  }

  .nav-center {
    flex: 2;
    display: flex;
    align-items: center;
    justify-content: center;

    .nav-title {
      font-size: 20px;
      text-align: center;
    }
  }

  .nav-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .nav-theme-btn {
      padding: 8px 16px;
      border: none;
      background: transparent;
      font-size: 18px;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.7;
      }

      &:active {
        opacity: 0.5;
      }
    }
  }
}

/* 小程序适配 */
@media screen and (max-width: 750px) {
  .navigation-bar {
    height: 64px;
    padding: 0 10px;

    .nav-left .nav-back-btn,
    .nav-right .nav-theme-btn {
      font-size: 16px;
      padding: 6px 12px;
    }

    .nav-center .nav-title {
      font-size: 20px;
    }
  }
}

/* H5适配 */
@media screen and (min-width: 751px) {
  .navigation-bar {
    height: 64px;
    padding: 0 10px;

    .nav-left .nav-back-btn,
    .nav-right .nav-theme-btn {
      font-size: 16px;
      padding: 8px 16px;
    }

    .nav-center .nav-title {
      font-size: 18px;
    }
  }
}
