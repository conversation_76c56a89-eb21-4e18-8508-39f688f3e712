// 基础加载指示器样式
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  &--small {
    gap: 4px;
  }

  &--medium {
    gap: 8px;
  }

  &--large {
    gap: 12px;
  }
}

.loading-indicator__message {
  font-size: 14px;
  color: #666;
  text-align: center;

  .dark & {
    color: #999;
  }

  .loading-indicator--small & {
    font-size: 12px;
  }

  .loading-indicator--large & {
    font-size: 16px;
  }
}

.loading-indicator__progress {
  font-size: 12px;
  color: #999;
  font-weight: 500;

  .loading-indicator--large & {
    font-size: 14px;
  }
}

// 旋转加载器
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;

  &--small {
    width: 16px;
    height: 16px;
  }

  &--medium {
    width: 24px;
    height: 24px;
  }

  &--large {
    width: 32px;
    height: 32px;
  }
}

.loading-spinner__circle {
  width: 100%;
  height: 100%;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  .dark & {
    border-color: #3a3a3a;
    border-top-color: #007aff;
  }

  .loading-spinner--small & {
    border-width: 1.5px;
  }

  .loading-spinner--large & {
    border-width: 3px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Quantum 加载器
.loading-quantum {
  display: flex;
  align-items: center;
  justify-content: center;

  &--small {
    width: 32px;
    height: 32px;
  }

  &--medium {
    width: 45px;
    height: 45px;
  }

  &--large {
    width: 60px;
    height: 60px;
  }
}

// 点状加载器
.loading-dots {
  display: flex;
  align-items: center;
  gap: 4px;

  &--small {
    gap: 2px;
  }

  &--medium {
    gap: 4px;
  }

  &--large {
    gap: 6px;
  }
}

.loading-dots__dot {
  width: 6px;
  height: 6px;
  background-color: #007aff;
  border-radius: 50%;
  animation: dot-bounce 1.4s infinite ease-in-out both;

  .loading-dots--small & {
    width: 4px;
    height: 4px;
  }

  .loading-dots--large & {
    width: 8px;
    height: 8px;
  }

  &:nth-child(1) {
    animation-delay: -0.32s;
  }
  &:nth-child(2) {
    animation-delay: -0.16s;
  }
  &:nth-child(3) {
    animation-delay: 0s;
  }
}

@keyframes dot-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 脉冲加载器
.loading-pulse {
  display: flex;
  align-items: center;
  justify-content: center;

  &--small {
    width: 16px;
    height: 16px;
  }

  &--medium {
    width: 24px;
    height: 24px;
  }

  &--large {
    width: 32px;
    height: 32px;
  }
}

.loading-pulse__circle {
  width: 100%;
  height: 100%;
  background-color: #007aff;
  border-radius: 50%;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

// 进度条加载器
.loading-bar {
  width: 100%;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;

  .dark & {
    background-color: #3a3a3a;
  }

  &--small {
    height: 2px;
  }

  &--medium {
    height: 4px;
  }

  &--large {
    height: 6px;
  }
}

.loading-bar__fill {
  height: 100%;
  background-color: #007aff;
  border-radius: inherit;
  transition: width 0.3s ease;
  animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// 全局加载覆盖层
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  &--backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }
}

.global-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 280px;

  .dark & {
    background: #1a1a1a;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
}

.global-loading-message {
  font-size: 16px;
  color: #333;
  text-align: center;
  font-weight: 500;

  .dark & {
    color: #fff;
  }
}

.global-loading-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.global-loading-progress-bar {
  width: 100%;
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;

  .dark & {
    background-color: #3a3a3a;
  }
}

.global-loading-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007aff, #00c6ff);
  border-radius: inherit;
  transition: width 0.3s ease;
}

.global-loading-progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
  font-weight: 500;

  .dark & {
    color: #999;
  }
}

// 内联加载指示器
.inline-loading {
  display: flex;
  align-items: center;
  gap: 8px;

  &--small {
    gap: 6px;
  }

  &--medium {
    gap: 8px;
  }
}

.inline-loading__text {
  font-size: 14px;
  color: #666;

  .dark & {
    color: #999;
  }

  .inline-loading--small & {
    font-size: 12px;
  }
}

// 按钮加载状态
.button-loading {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;

  &--small {
    padding: 8px 16px;
    font-size: 12px;
    min-height: 32px;
  }

  &--medium {
    padding: 12px 24px;
    font-size: 14px;
    min-height: 40px;
  }

  &--large {
    padding: 16px 32px;
    font-size: 16px;
    min-height: 48px;
  }

  &--primary {
    background-color: #007aff;
    color: #ffffff;

    &:hover:not(.button-loading--disabled) {
      background-color: #0056cc;
    }
  }

  &--secondary {
    background-color: #f0f0f0;
    color: #333;

    .dark & {
      background-color: #2a2a2a;
      color: #fff;
    }

    &:hover:not(.button-loading--disabled) {
      background-color: #e0e0e0;

      .dark & {
        background-color: #3a3a3a;
      }
    }
  }

  &--ghost {
    background-color: transparent;
    color: #007aff;
    border: 1px solid #007aff;

    &:hover:not(.button-loading--disabled) {
      background-color: #007aff;
      color: #ffffff;
    }
  }

  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.button-loading__indicator {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.button-loading__content {
  transition: opacity 0.2s ease;

  &--loading {
    opacity: 0;
  }
}

// 卡片加载状态
.card-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  border-radius: 12px;
  border: 1px solid #e0e0e0;

  .dark & {
    background: #1a1a1a;
    border-color: #3a3a3a;
  }
}

// 页面加载状态
.page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 40px 20px;
}

.page-loading__message {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
  text-align: center;

  .dark & {
    color: #999;
  }
}

// 加载更多
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover:not(.load-more--disabled) {
    background-color: #f8f8f8;

    .dark & {
      background-color: #2a2a2a;
    }
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.load-more__text {
  font-size: 14px;
  color: #007aff;
  font-weight: 500;

  .load-more--disabled & {
    color: #999;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .global-loading-content {
    padding: 24px;
    max-width: 240px;
  }

  .global-loading-message {
    font-size: 14px;
  }

  .button-loading {
    &--small {
      padding: 6px 12px;
      min-height: 28px;
    }

    &--medium {
      padding: 10px 20px;
      min-height: 36px;
    }

    &--large {
      padding: 14px 28px;
      min-height: 44px;
    }
  }
}

// 小程序特殊样式
@media (max-width: 480px) {
  .global-loading-overlay {
    padding: 20px;
  }

  .global-loading-content {
    padding: 20px;
    max-width: 200px;
  }

  .page-loading {
    min-height: 150px;
    padding: 30px 15px;
  }
}
