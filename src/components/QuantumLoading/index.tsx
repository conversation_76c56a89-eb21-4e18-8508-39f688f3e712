import { Quantum } from 'ldrs/react';
import 'ldrs/react/Quantum.css';
import React from 'react';

export interface QuantumLoadingProps {
  /** 加载器大小 */
  size?: string;
  /** 动画速度 */
  speed?: string;
  /** 颜色 */
  color?: string;
  /** 自定义类名 */
  className?: string;
}

const QuantumLoading: React.FC<QuantumLoadingProps> = ({
  size = '45',
  speed = '1.75',
  color = 'black',
  className,
}) => {
  return (
    <div className={className}>
      <Quantum size={size} speed={speed} color={color} />
    </div>
  );
};

export default QuantumLoading;
