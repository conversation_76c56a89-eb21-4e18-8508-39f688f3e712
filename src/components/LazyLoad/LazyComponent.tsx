import { View } from '@tarojs/components';
import React, { ComponentType, Suspense, lazy } from 'react';
import { useInView } from 'react-intersection-observer';
import Skeleton from '../Skeleton/Skeleton';

interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  className?: string;
}

// 懒加载组件包装器
const LazyComponent: React.FC<LazyComponentProps> = ({
  children,
  fallback = <Skeleton height='200px' />,
  threshold = 0.1,
  rootMargin = '50px',
  triggerOnce = true,
  className,
}) => {
  const { ref, inView } = useInView({
    threshold,
    rootMargin,
    triggerOnce,
  });

  return (
    <View ref={ref} className={className}>
      {inView ? children : fallback}
    </View>
  );
};

// 创建懒加载组件的高阶函数
export function createLazyComponent<P extends object>(
  importFunc: () => Promise<{ default: ComponentType<P> }>,
  fallback?: React.ReactNode
) {
  const LazyLoadedComponent = lazy(importFunc);

  return React.forwardRef<any, P>((props, ref) => {
    return (
      <Suspense fallback={fallback || <Skeleton height='200px' />}>
        <LazyLoadedComponent {...props} ref={ref} />
      </Suspense>
    );
  });
}

// 视口懒加载高阶组件
export function withViewportLazyLoad<P extends object>(
  Component: ComponentType<P>,
  options?: {
    threshold?: number;
    rootMargin?: string;
    fallback?: React.ReactNode;
  }
) {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    fallback = <Skeleton height='200px' />,
  } = options || {};

  return React.forwardRef<any, P>((props, ref) => {
    const { ref: inViewRef, inView } = useInView({
      threshold,
      rootMargin,
      triggerOnce: true,
    });

    return (
      <View ref={inViewRef}>
        {inView ? <Component {...(props as P)} ref={ref} /> : fallback}
      </View>
    );
  });
}

// 延迟加载高阶组件
export function withDelayedLoad<P extends object>(
  Component: ComponentType<P>,
  delay: number = 100,
  fallback?: React.ReactNode
) {
  return React.forwardRef<any, P>((props, ref) => {
    const [shouldLoad, setShouldLoad] = React.useState(false);

    React.useEffect(() => {
      const timer = setTimeout(() => {
        setShouldLoad(true);
      }, delay);

      return () => clearTimeout(timer);
    }, [delay]);

    if (!shouldLoad) {
      return <>{fallback || <Skeleton height='200px' />}</>;
    }

    return <Component {...(props as P)} ref={ref} />;
  });
}

// 条件懒加载高阶组件
export function withConditionalLazyLoad<P extends object>(
  Component: ComponentType<P>,
  condition: (props: P) => boolean,
  fallback?: React.ReactNode
) {
  return React.forwardRef<any, P>((props, ref) => {
    const shouldLoad = condition(props as P);

    if (!shouldLoad) {
      return <>{fallback || <Skeleton height='200px' />}</>;
    }

    return <Component {...(props as P)} ref={ref} />;
  });
}

// 预加载组件的工具函数
export function preloadComponent<P extends object>(
  importFunc: () => Promise<{ default: ComponentType<P> }>
): Promise<ComponentType<P>> {
  return importFunc().then(module => module.default);
}

// 批量预加载组件
export function preloadComponents(
  importFuncs: Array<() => Promise<{ default: ComponentType<any> }>>
): Promise<ComponentType<any>[]> {
  return Promise.all(importFuncs.map(preloadComponent));
}

// 懒加载路由组件
export function createLazyRoute<P extends object>(
  importFunc: () => Promise<{ default: ComponentType<P> }>,
  fallback?: React.ReactNode
) {
  const LazyRouteComponent = lazy(importFunc);

  return React.forwardRef<any, P>((props, ref) => (
    <Suspense fallback={fallback || <Skeleton height='100vh' />}>
      <LazyRouteComponent {...props} ref={ref} />
    </Suspense>
  ));
}

// 智能懒加载组件（结合视口和延迟）
export function withSmartLazyLoad<P extends object>(
  Component: ComponentType<P>,
  options?: {
    threshold?: number;
    rootMargin?: string;
    delay?: number;
    fallback?: React.ReactNode;
  }
) {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    delay = 0,
    fallback = <Skeleton height='200px' />,
  } = options || {};

  return React.forwardRef<any, P>((props, ref) => {
    const [shouldLoad, setShouldLoad] = React.useState(false);
    const { ref: inViewRef, inView } = useInView({
      threshold,
      rootMargin,
      triggerOnce: true,
    });

    React.useEffect(() => {
      if (inView) {
        if (delay > 0) {
          const timer = setTimeout(() => {
            setShouldLoad(true);
          }, delay);
          return () => clearTimeout(timer);
        } else {
          setShouldLoad(true);
        }
      }
    }, [inView, delay]);

    return (
      <View ref={inViewRef}>
        {shouldLoad ? <Component {...(props as P)} ref={ref} /> : fallback}
      </View>
    );
  });
}

export default LazyComponent;
