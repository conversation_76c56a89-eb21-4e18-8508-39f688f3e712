import { Image, View } from '@tarojs/components';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import { platform } from '../../utils/platform';
import { SkeletonImage } from '../Skeleton/Skeleton';

interface LazyImageProps {
  src: string;
  alt?: string;
  className?: string;
  width?: string | number;
  height?: string | number;
  aspectRatio?: number;
  mode?: 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix';
  placeholder?: string;
  fallback?: string;
  threshold?: number;
  loading?: 'eager' | 'lazy';
  onLoad?: () => void;
  onError?: () => void;
  showSkeleton?: boolean;
  fadeIn?: boolean;
  blur?: boolean;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt = '',
  className,
  width,
  height,
  aspectRatio = 16 / 9,
  mode = 'aspectFill',
  placeholder,
  fallback = 'https://via.placeholder.com/400x300?text=Image+Not+Found',
  threshold = 0.1,
  loading = 'lazy',
  onLoad,
  onError,
  showSkeleton = true,
  fadeIn = true,
  blur = false,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>(placeholder || '');

  // 使用IntersectionObserver监听元素是否进入视口
  const { ref, inView } = useInView({
    threshold,
    triggerOnce: true,
  });

  // 计算容器样式
  const containerStyle: React.CSSProperties = {
    width: width || '100%',
    height: height || (aspectRatio ? 'auto' : '100%'),
    position: 'relative',
    overflow: 'hidden',
  };

  // 计算图片包装器样式
  const wrapperStyle: React.CSSProperties = {
    position: 'relative',
    width: '100%',
    paddingBottom:
      !height && aspectRatio ? `${(1 / aspectRatio) * 100}%` : undefined,
    height: height ? height : undefined,
  };

  // 计算图片样式
  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: height ? height : '100%',
    objectFit:
      mode === 'aspectFit'
        ? 'contain'
        : mode === 'aspectFill'
        ? 'cover'
        : undefined,
    opacity: isLoaded ? 1 : 0,
    transition: fadeIn ? 'opacity 0.3s ease-in-out' : undefined,
    filter: blur && !isLoaded ? 'blur(10px)' : undefined,
  };

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  // 处理图片加载错误
  const handleError = () => {
    setIsError(true);
    setCurrentSrc(fallback);
    onError?.();
  };

  // 当元素进入视口或loading为eager时加载图片
  useEffect(() => {
    if ((inView || loading === 'eager') && src) {
      setCurrentSrc(src);
    }
  }, [inView, loading, src]);

  // 小程序环境下的懒加载处理
  if (platform.isMiniProgram()) {
    return (
      <View
        className={classNames('lazy-image-container', className)}
        style={containerStyle}
      >
        <View className='lazy-image-wrapper' style={wrapperStyle}>
          {showSkeleton && !isLoaded && (
            <SkeletonImage
              width='100%'
              height='100%'
              aspectRatio={aspectRatio}
            />
          )}
          <Image
            src={src}
            mode={mode}
            lazyLoad={loading === 'lazy'}
            className={classNames('lazy-image', {
              'lazy-image-loaded': isLoaded,
            })}
            style={imageStyle}
            onLoad={handleLoad}
            onError={handleError}
          />
        </View>
      </View>
    );
  }

  // H5环境下的懒加载处理
  return (
    <View
      ref={ref}
      className={classNames('lazy-image-container', className)}
      style={containerStyle}
    >
      <View className='lazy-image-wrapper' style={wrapperStyle}>
        {showSkeleton && !isLoaded && (
          <SkeletonImage width='100%' height='100%' aspectRatio={aspectRatio} />
        )}
        {currentSrc && (
          <Image
            src={currentSrc}
            mode={mode}
            className={classNames('lazy-image', {
              'lazy-image-loaded': isLoaded,
            })}
            style={imageStyle}
            onLoad={handleLoad}
            onError={handleError}
          />
        )}
      </View>
    </View>
  );
};

export default LazyImage;
