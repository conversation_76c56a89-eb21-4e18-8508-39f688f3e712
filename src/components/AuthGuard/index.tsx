import Taro from '@tarojs/taro';
import React, { useEffect } from 'react';
import { useUserStore } from '../../stores/userStore';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean; // 是否需要登录
  redirectTo?: string; // 未登录时跳转的页面
}

const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/pages/auth/index',
}) => {
  const { isAuthenticated, user } = useUserStore();

  useEffect(() => {
    if (requireAuth && !isAuthenticated) {
      // 如果需要登录但用户未登录，跳转到登录页面
      Taro.redirectTo({
        url: redirectTo,
      });
    }
  }, [isAuthenticated, requireAuth, redirectTo]);

  // 如果需要登录但用户未登录，不渲染子组件
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  return <>{children}</>;
};

export default AuthGuard;
