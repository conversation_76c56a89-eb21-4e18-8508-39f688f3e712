import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import './index.scss';

interface TabBarProps {
  current?: number;
  onChange?: (index: number) => void;
}

interface TabItem {
  title: string;
  url: string;
  icon?: string;
}

const tabList: TabItem[] = [
  {
    title: '资讯',
    url: '/pages/index/index',
    icon: '',
  },
  {
    title: '分析',
    url: '/pages/market-analysis/index',
    icon: '',
  },
  {
    title: '我的',
    url: '/pages/profile/index',
    icon: '',
  },
];

function TabBar({ current = 0, onChange }: TabBarProps) {
  const [activeIndex, setActiveIndex] = useState(current);

  useEffect(() => {
    setActiveIndex(current);
  }, [current]);

  const handleTabClick = (index: number, url: string) => {
    console.log('点击了TabBar', { index, activeIndex, url });

    // 移除这个判断，允许重复点击同一个tab
    // if (index === activeIndex) return;

    // 添加简单的点击反馈
    const clickedElement = document.querySelector(
      `.tab-bar__item:nth-child(${index + 1})`
    );
    if (clickedElement) {
      clickedElement.classList.add('tab-bar__item--clicking');
      setTimeout(() => {
        clickedElement.classList.remove('tab-bar__item--clicking');
      }, 150);
    }

    setActiveIndex(index);
    onChange?.(index);

    // 直接跳转，不添加会影响布局的动画
    // 由于没有配置原生tabBar，所有页面都使用navigateTo
    Taro.navigateTo({ url });
  };

  return (
    <View className='tab-bar'>
      <View className='tab-bar__container'>
        {tabList.map((tab, index) => (
          <View
            key={index}
            className={`tab-bar__item ${
              index === activeIndex ? 'tab-bar__item--active' : ''
            } ${index === 1 ? 'tab-bar__item--center' : ''}`}
            onClick={() => handleTabClick(index, tab.url)}
          >
            {/* 分析按钮特殊样式 */}
            {index === 1 ? (
              <>
                <View className='tab-bar__analysis-btn'>
                  <Text className='tab-bar__title'>{tab.title}</Text>
                </View>
              </>
            ) : (
              <Text className='tab-bar__title'>{tab.title}</Text>
            )}
          </View>
        ))}
      </View>
    </View>
  );
}

export default TabBar;
