.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 0.05rem solid #d0d0d0;
  z-index: 999;
  height: 4rem;

  &__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    height: 100%;
    max-width: 30rem;
    margin: 0 auto;
    position: relative;
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    height: 100%;
    position: relative;

    // 普通按钮样式（资讯、我的）
    &:not(.tab-bar__item--center) {
      background: #fff;
      color: #333;
      flex: 0 0 4rem;
      .tab-bar__title {
        color: #333;
        font-size: 0.8rem;
        font-weight: 500;
      }
    }

    &--active {
      .tab-bar__title {
        color: #333;
        font-weight: 600;
      }
    }

    &--clicking {
      transform: scale(0.98);
      transition: transform 0.1s ease-out;
    }
  }

  // 分析按钮特殊样式
  &__item--center {
    position: relative;
    flex: 1;

    .tab-bar__analysis-btn {
      width: 6rem;
      height: 6rem;
      background: #ff0000;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      position: relative;
      top: -1.2rem;
      margin: 0 auto;

      .tab-bar__title {
        color: #fff;
        font-size: 0.8rem;
        font-weight: 600;
      }

      &:hover {
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  &__title {
    font-size: 0.8rem;
    color: #333;
    text-align: center;
    line-height: 1.2;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  // 响应式设计
  @media (max-width: 30rem) {
    height: 3.5rem;

    &__container {
      padding: 0 0.5rem;
    }

    &__item:not(.tab-bar__item--center) {
      flex: 0 0 3rem;
    }

    &__item--center .tab-bar__analysis-btn {
      width: 3.5rem;
      height: 3.5rem;
      top: -1.0667rem;

      .tab-bar__title {
        font-size: 0.7rem;
      }
    }

    &__title {
      font-size: 0.7rem;
    }
  }

  @media (max-width: 22.5rem) {
    height: 3rem;

    &__container {
      padding: 0 0.3rem;
    }

    &__item:not(.tab-bar__item--center) {
      flex: 0 0 2.5rem;
    }

    &__item--center .tab-bar__analysis-btn {
      width: 4rem;
      height: 4rem;
      top: -0.5333rem;

      .tab-bar__title {
        font-size: 0.6rem;
      }
    }

    &__title {
      font-size: 0.6rem;
    }
  }
}
