import React from 'react';
import type { Platform } from '../../types';
import { platform } from '../../utils/platform';

interface PlatformRenderProps {
  children: React.ReactNode;
  platform?: Platform | Platform[];
  exclude?: Platform | Platform[];
  miniProgram?: React.ReactNode;
  h5?: React.ReactNode;
  rn?: React.ReactNode;
  weapp?: React.ReactNode;
  alipay?: React.ReactNode;
  swan?: React.ReactNode;
  tt?: React.ReactNode;
  qq?: React.ReactNode;
  jd?: React.ReactNode;
}

const PlatformRender: React.FC<PlatformRenderProps> = ({
  children,
  platform: targetPlatform,
  exclude,
  miniProgram,
  h5,
  rn,
  weapp,
  alipay,
  swan,
  tt,
  qq,
  jd,
}) => {
  // 如果指定了特定平台的内容，优先渲染
  if (weapp && platform.isWeapp()) return <>{weapp}</>;
  if (alipay && platform.isAlipay()) return <>{alipay}</>;
  if (swan && platform.isSwan()) return <>{swan}</>;
  if (tt && platform.isTt()) return <>{tt}</>;
  if (qq && platform.isQq()) return <>{qq}</>;
  if (jd && platform.isJd()) return <>{jd}</>;
  if (h5 && platform.isH5()) return <>{h5}</>;
  if (rn && platform.isRn()) return <>{rn}</>;
  if (miniProgram && platform.isMiniProgram()) return <>{miniProgram}</>;

  // 检查排除条件
  if (exclude) {
    const excludeList = Array.isArray(exclude) ? exclude : [exclude];
    const currentPlatform = process.env.TARO_ENV as Platform;
    if (excludeList.includes(currentPlatform)) {
      return null;
    }
  }

  // 检查包含条件
  if (targetPlatform) {
    const platformList = Array.isArray(targetPlatform)
      ? targetPlatform
      : [targetPlatform];
    const currentPlatform = process.env.TARO_ENV as Platform;
    if (!platformList.includes(currentPlatform)) {
      return null;
    }
  }

  return <>{children}</>;
};

// 便捷的平台条件组件
export const MiniProgramOnly: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender miniProgram={children}>{children}</PlatformRender>;

export const H5Only: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender h5={children}>{children}</PlatformRender>;

export const RNOnly: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender rn={children}>{children}</PlatformRender>;

export const WeappOnly: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender weapp={children}>{children}</PlatformRender>;

export const AlipayOnly: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender alipay={children}>{children}</PlatformRender>;

export const SwanOnly: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender swan={children}>{children}</PlatformRender>;

export const TtOnly: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender tt={children}>{children}</PlatformRender>;

export const QqOnly: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender qq={children}>{children}</PlatformRender>;

export const JdOnly: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender jd={children}>{children}</PlatformRender>;

export const ExcludeMiniProgram: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => (
  <PlatformRender exclude={['weapp', 'alipay', 'swan', 'tt', 'qq', 'jd']}>
    {children}
  </PlatformRender>
);

export const ExcludeH5: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender exclude='h5'>{children}</PlatformRender>;

export const ExcludeRN: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <PlatformRender exclude='rn'>{children}</PlatformRender>;

export default PlatformRender;
