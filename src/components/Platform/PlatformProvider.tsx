import { View } from '@tarojs/components';
import classNames from 'classnames';
import React, { useEffect } from 'react';
import { useAppStore } from '../../stores/appStore';
import { getCurrentPlatform, platform } from '../../utils/platform';

interface PlatformProviderProps {
  children: React.ReactNode;
}

const PlatformProvider: React.FC<PlatformProviderProps> = ({ children }) => {
  const { updateDeviceInfo } = useAppStore();

  useEffect(() => {
    // 初始化设备信息
    updateDeviceInfo();
  }, [updateDeviceInfo]);

  const currentPlatform = getCurrentPlatform();

  const containerClasses = classNames(
    'platform-container',
    `platform-${currentPlatform}`,
    {
      'is-miniprogram': platform.isMiniProgram(),
      'is-web': platform.isWeb(),
      'is-mobile': platform.isMobile(),
    }
  );

  return <View className={containerClasses}>{children}</View>;
};

export default PlatformProvider;
