export type DebounceDefaults = {
  wait: number;
  maxWait?: number;
  leading?: boolean;
  trailing?: boolean;
};

export type ThrottleDefaults = {
  interval: number;
  limit: number;
  leading?: boolean;
  trailing?: boolean;
};

export const rateLimitConfig: {
  debounce: DebounceDefaults;
  throttle: ThrottleDefaults;
} = {
  debounce: {
    wait: 500,
    maxWait: 2000,
    leading: false,
    trailing: true,
  },
  throttle: {
    interval: 1000,
    limit: 1,
    leading: true,
    trailing: true,
  },
};

export function setRateLimitConfig(
  overrides: Partial<{
    debounce: Partial<DebounceDefaults>;
    throttle: Partial<ThrottleDefaults>;
  }>
) {
  if (overrides.debounce) {
    rateLimitConfig.debounce = {
      ...rateLimitConfig.debounce,
      ...overrides.debounce,
    };
  }
  if (overrides.throttle) {
    rateLimitConfig.throttle = {
      ...rateLimitConfig.throttle,
      ...overrides.throttle,
    };
  }
}
