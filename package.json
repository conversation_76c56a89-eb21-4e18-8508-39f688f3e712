{"name": "crypto-taro", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "react-NutUI", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "build:weapp:prod": "NODE_ENV=production taro build --type weapp && node scripts/build-optimization.js", "build:h5:prod": "NODE_ENV=production taro build --type h5 && node scripts/build-optimization.js", "build:h5:analyze": "ANALYZE=true NODE_ENV=production taro build --type h5", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "optimize:images": "node scripts/build-optimization.js", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "test": "echo \"No tests specified\" && exit 0"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@nutui/icons-react-taro": "^3.0.1", "@nutui/nutui-react-taro": "^2.6.14", "@tarojs/components": "4.1.4", "@tarojs/helper": "4.1.4", "@tarojs/plugin-framework-react": "4.1.4", "@tarojs/plugin-html": "4.1.4", "@tarojs/plugin-platform-alipay": "4.1.4", "@tarojs/plugin-platform-h5": "4.1.4", "@tarojs/plugin-platform-jd": "4.1.4", "@tarojs/plugin-platform-qq": "4.1.4", "@tarojs/plugin-platform-swan": "4.1.4", "@tarojs/plugin-platform-tt": "4.1.4", "@tarojs/plugin-platform-weapp": "4.1.4", "@tarojs/react": "4.1.4", "@tarojs/runtime": "4.1.4", "@tarojs/shared": "4.1.4", "@tarojs/taro": "4.1.4", "axios": "^1.11.0", "classnames": "^2.5.1", "ldrs": "^1.1.7", "react": "^18.0.0", "react-dom": "^18.0.0", "react-intersection-observer": "^9.16.0", "react-lazyload": "^3.2.1", "zustand": "^4.5.6"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tarojs/cli": "4.1.4", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/vite-runner": "4.1.4", "@types/node": "^24.1.0", "@types/react": "^18.0.0", "@types/react-lazyload": "^3.2.3", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.21", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "4.1.4", "eslint": "^8.12.0", "eslint-config-taro": "4.1.4", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "imagemin": "^9.0.1", "imagemin-gifsicle": "^7.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-svgo": "^11.0.1", "postcss": "^8.5.6", "react-refresh": "^0.11.0", "sass": "^1.60.0", "stylelint": "^14.4.0", "tailwindcss": "^4.1.11", "terser": "^5.16.8", "typescript": "^5.1.0", "vite": "^4.2.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-imp": "^2.4.0", "weapp-tailwindcss": "^4.2.2"}}